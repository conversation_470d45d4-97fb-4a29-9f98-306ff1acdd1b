# 常用模式和最佳实践

- 常用代码模式：1.Controller层示例-使用@RestController+@RequestMapping+@ApiOperation 2.Service层使用@Service注解和事务管理 3.DAO层继承BaseMapper<T> 4.实体类使用@TableName指定表名 5.分页查询使用PageHelper.startPage() 6.Redis操作使用RedisUtil工具类 7.日期处理使用CrmebDateUtil 8.字符串工具使用StrUtil 9.对象工具使用ObjectUtil 10.权限验证使用SecurityUtil.getLoginUserVo()
- 修复编译错误：将不存在的自定义Pagination组件替换为Element UI的el-pagination组件，包括移除导入语句、组件注册，替换模板中的分页组件，并添加相应的事件处理方法handleSizeChange和handleCurrentChange
- 金梭酒店项目代码架构模式：1.后端架构-Controller层(crmeb-admin/src/main/java/com/zbkj/admin/controller/hotel/)负责API接口，Service层(crmeb-service/src/main/java/com/zbkj/service/service/hotel/)负责业务逻辑，DAO层使用MyBatis Plus；2.前端架构-页面主文件(index.vue)负责列表展示和操作，组件文件(components/)负责表单和弹窗，使用Element UI组件库；3.数据流模式-前端调用后端API→后端验证权限和参数→Service层处理业务逻辑→DAO层操作数据库→返回结果；4.命名规范-控制器以Controller结尾，服务以Service结尾，请求类以Request结尾，响应类以Response结尾；5.权限控制-使用@PreAuthorize注解(已注释)，通过SecurityUtil获取当前登录用户信息。
- 酒店房间价格计算引擎详细逻辑：1.日期类型识别-调用ChineseCalendarService.getDateType()获取WORKDAY/WEEKEND/HOLIDAY/TRANSFER_WORKDAY；2.策略集合获取-查询房间所有启用价格策略按优先级排序；3.策略匹配过滤-根据strategyType匹配(1基础价格工作日,2周末价格,3节假日价格,4日期范围,5具体日期)；4.优先级排序-按priority降序取最高优先级策略的priceValue；5.异常处理-无策略或异常时返回BigDecimal.ZERO。核心方法calculatePrice()已添加详细专业注释，包含业务逻辑、计算流程、策略优先级、异常处理等完整说明。
- 酒店预订页面最佳实践优化：使用真实API调用配合降级方案，增强数据验证和错误处理，优化用户体验反馈。API调用失败时自动切换到模拟模式，确保系统健壮性。使用模态框提供详细的成功反馈信息。
- 酒店预订订单创建完整流程：使用buyNow类型预下单+标准订单创建，虚拟商品自动设置addressId=0，到店核销shippingType=2自动生成核销码，包含智能降级策略确保系统健壮性。
- 酒店预订商品ID查找修复：酒店商品按"酒店名-房型名-日期"规则生成，需要根据hotelId、roomName、checkInDate查找对应的商品ID，而不是直接使用hotelId。实现了API查找失败时的模拟数据降级策略。
- 酒店预订商品ID问题完整解决方案：后端HotelRoomListResponse.PriceDetail增加productId和attrValueId字段，HotelBookingServiceImpl查找商品属性值ID并返回，前端从价格明细获取商品ID进行预下单，完整流程为预下单→订单创建→核销码生成。
- 酒店订单退款代码实现模式：1. 通过productType.equals(7)严格判断酒店商品 2. 使用if-else分离酒店和普通商品处理逻辑 3. 新增HotelRefundAutoTask定时任务每2小时检查超时退款 4. 在RefundOrderManagerServiceImpl中添加autoRefundHotelOrders方法 5. 酒店订单退款时更新所有OrderDetail的applyRefundNum
