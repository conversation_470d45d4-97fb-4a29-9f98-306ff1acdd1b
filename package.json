{"name": "crmeb-java-webpc-admin", "version": "4.2.1", "description": "Java mall free open source CRMEB mall JAVA version, SpringBoot + Maven + Swagger + Mybatis Plus + Redis + Uniapp +Vue+elementUI Including mobile terminal, applet, PC background, Api interface; products, users, shopping carts, orders, points, Modules such as coupons, marketing, balance, permissions, roles, system settings, combined data, and drag-and-drop forms have greatly reduced the cost of second-opening.", "author": "CRMEB", "license": "MIT", "scripts": {"dev": "vue-cli-service serve --open", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "prettier:comment": "自动格式化当前目录下的所有文件", "prettier": "prettier --write .", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "new": "plop", "serve_t": "set NODE_OPTIONS=\"--openssl-legacy-provider\" & npm run serve\n"}, "keywords": ["CRMEB Java"], "repository": {"type": "gitee", "url": "https://gitee.com/ZhongBangKeJi/crmeb_java"}, "dependencies": {"@babel/parser": "^7.9.6", "@riophae/vue-treeselect": "0.4.0", "async-validator": "^1.11.2", "axios": "^0.24.0", "babel-polyfill": "^6.26.0", "clipboard": "^2.0.4", "core-js": "^2.6.11", "crypto-js": "^4.2.0", "echarts": "4.2.1", "element-ui": "2.15.6", "file-saver": "2.0.1", "fuse.js": "3.4.4", "js-cookie": "2.2.0", "jsonlint": "1.6.3", "jszip": "3.2.1", "mpvue-calendar": "^2.3.7", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "qrcodejs2": "^0.0.2", "sass": "1.26.2", "sass-loader": "^7.2.0", "screenfull": "^5.0.2", "script-loader": "0.7.2", "throttle-debounce": "^2.1.0", "vconsole": "^3.3.2", "vue": "2.6.10", "vue-awesome-swiper": "^3.1.3", "vue-cropper": "^0.5.8", "vue-echarts": "^4.0.3", "vue-router": "3.0.2", "vue-ydui": "^1.2.6", "vuedraggable": "^2.20.0", "vuex": "3.1.0", "wechat-jssdk": "^5.0.4", "xlsx": "0.14.1"}, "devDependencies": {"@babel/core": "7.23.5", "@babel/register": "7.0.0", "@vue/cli-plugin-babel": "3.5.3", "@vue/cli-plugin-unit-jest": "3.5.3", "@vue/cli-service": "4.0.0", "@vue/test-utils": "1.0.0-beta.29", "babel-core": "7.0.0-bridge.0", "babel-jest": "23.6.0", "chalk": "2.4.2", "chokidar": "^3.1.1", "connect": "3.6.6", "html-webpack-plugin": "3.2.0", "mockjs": "1.0.1-beta3", "plop": "2.3.0", "prettier": "^2.5.1", "runjs": "^4.3.2", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "^0.7.2", "serve-static": "^1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "vue-lazyload": "^1.3.3", "vue-template-compiler": "2.6.10"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}