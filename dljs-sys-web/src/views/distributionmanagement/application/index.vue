<template>
  <div class="divBox">
    <el-card
      :bordered="false"
      shadow="never"
      class="ivu-mt"
      :body-style="{ padding: 0 }"
      v-hasPermi="['platform:retail:store:people:list']"
    >
      <div class="padding-add">
        <el-form size="small" inline @submit.native.prevent>
          <el-form-item label="时间选择：">
            <el-date-picker
              v-model="timeVal"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              size="small"
              type="daterange"
              placement="bottom-end"
              placeholder="自定义时间"
              style="width: 260px"
              @change="onchangeTime"
            />
          </el-form-item>
          <el-form-item label="用户搜索：" label-for="nickname">
            <UserSearchInput v-model="promoterKeywords" @keyup.enter.native="handleSearch" />
          </el-form-item>
          <el-form-item label="申请状态：">
            <el-select v-model="tableFrom.status" placeholder="请选择" clearable @change="getList(1)">
              <el-option label="全部" value=""></el-option>
              <el-option label="待审核" value="0"></el-option>
              <el-option label="已通过" value="1"></el-option>
              <el-option label="已拒绝" value="-1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" @click="handleSearch">查询</el-button>
            <el-button size="small" @click="reset()">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <el-card class="box-card mt14" :body-style="{ padding: '20px' }" shadow="never" :bordered="false">
      <el-table
        v-loading="listLoading"
        :data="tableData.data"
        style="width: 100%"
        size="small"
        class="table"
        highlight-current-row
      >
        <el-table-column prop="id" label="ID" width="60" />
        <el-table-column label="头像" min-width="80">
          <template slot-scope="scope">
            <div class="demo-image__preview line-heightOne">
              <el-image 
                :src="scope.row.avatar ? getImgUrl(scope.row.avatar)[0] : ''" 
                :preview-src-list="scope.row.avatar ? getImgUrl(scope.row.avatar) : []" 
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="nickname" label="用户信息" min-width="130" :show-overflow-tooltip="true" />
        <el-table-column prop="applyTime" label="申请时间" min-width="150" />
        <el-table-column prop="applyReason" label="申请理由" min-width="200" :show-overflow-tooltip="true" />
        <el-table-column prop="status" label="状态" min-width="100">
          <template slot-scope="scope">
            <el-tag v-if="Number(scope.row.status) === 0" type="info">待审核</el-tag>
            <el-tag v-else-if="Number(scope.row.status) === 1" type="success">已通过</el-tag>
            <el-tag v-else-if="Number(scope.row.status) === -1" type="danger">已拒绝</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="auditTime" label="审核时间" min-width="150" />
        <el-table-column prop="auditReason" label="审核备注" min-width="200" :show-overflow-tooltip="true" />
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <a @click="handleAudit(scope.row, 1)" v-if="Number(scope.row.status) === 0" v-hasPermi="['platform:retail:store:people:list']"
              >审核通过</a
            >
            <el-divider v-if="Number(scope.row.status) === 0" direction="vertical"></el-divider>
            <a @click="handleAudit(scope.row, -1)" v-if="Number(scope.row.status) === 0" v-hasPermi="['platform:retail:store:people:list']"
              >审核拒绝</a
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          background
          :page-sizes="[20, 40, 60, 80]"
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </el-card>

    <!-- 审核弹窗 -->
    <el-dialog title="推广员申请审核" :visible.sync="dialogVisible" width="500px" :before-close="handleClose">
      <el-form :model="auditForm" ref="auditForm" :rules="auditRules" label-width="100px">
        <el-form-item label="审核结果：">
          <el-tag v-if="Number(auditForm.status) === 1" type="success">通过</el-tag>
          <el-tag v-else-if="Number(auditForm.status) === -1" type="danger">拒绝</el-tag>
        </el-form-item>
        <el-form-item label="审核备注：" prop="auditReason">
          <el-input
            v-model="auditForm.auditReason"
            type="textarea"
            :rows="4"
            placeholder="请输入审核备注"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitAudit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { distributionapplicationListApi, distributionapplicationAuditApi } from '@/api/distributionmanagement/distributionapplication';
import { checkPermi } from '@/utils/permission'; // 权限判断函数
import UserSearchInput from '@/components/base/UserSearchInput';
import { getImgUrl } from "@/utils/imgUtil";

export default {
  name: 'DistributionApplication',
  components: {
    UserSearchInput
  },
  data() {
    return {
      timeVal: [],
      tableData: {
        data: [],
        total: 0,
      },
      listLoading: false,
      tableFrom: {
        dateLimit: '',
        keywords: '',
        page: 1,
        limit: 20,
        searchType: 'all',
        content: '',
        status: '',
      },
      promoterKeywords: '',
      dialogVisible: false,
      auditForm: {
        id: null,
        status: 0,
        auditReason: '',
      },
      auditRules: {
        auditReason: [
          { required: true, message: '请输入审核备注', trigger: 'blur' },
          { min: 2, max: 200, message: '长度在 2 到 200 个字符', trigger: 'blur' },
        ],
      },
    };
  },
  mounted() {
    if (checkPermi(['platform:retail:store:people:list'])) this.getList();
  },
  methods: {
    checkPermi,
    getImgUrl,
    // 处理审核
    handleAudit(row, status) {
      this.auditForm = {
        id: row.id,
        status: status,
        auditReason: '',
      };
      this.dialogVisible = true;
    },
    // 关闭弹窗
    handleClose() {
      this.dialogVisible = false;
      this.auditForm = {
        id: null,
        status: 0,
        auditReason: '',
      };
    },
    // 提交审核
    submitAudit() {
      this.$refs.auditForm.validate((valid) => {
        console.log(valid);
        console.log("thisauditForm",this.auditForm);
        
        
        if (valid) {
          distributionapplicationAuditApi(this.auditForm)
            .then((res) => {
              console.log("resssssssssss",res);
              
              this.$message.success(Number(this.auditForm.status) === 1 ? '审核通过成功' : '审核拒绝成功');
              this.dialogVisible = false;
              this.getList(1);
            })
            .catch(() => {
              console.log("失败了");
              
            });
        }
      });
    },
    // 选择时间
    selectChange(tab) {
      this.tableFrom.dateLimit = tab;
      this.timeVal = [];
      this.getList(1);
    },
    // 具体日期
    onchangeTime(e) {
      this.timeVal = e;
      this.tableFrom.dateLimit = e ? this.timeVal.join(',') : '';
      this.getList(1);
    },
    // 列表
    getList(num) {
      this.listLoading = true;
      this.tableFrom.page = num ? num : this.tableFrom.page;
      this.tableFrom.keywords = encodeURIComponent(this.promoterKeywords);
      distributionapplicationListApi(this.tableFrom)
        .then((res) => {
          this.tableData.data = res.list;
          this.tableData.total = res.total;
          this.listLoading = false;
        })
        .catch(() => {
          this.listLoading = false;
        });
    },
    reset() {
      this.tableFrom.dateLimit = '';
      this.tableFrom.keywords = '';
      this.tableFrom.searchType = 'all';
      this.tableFrom.content = '';
      this.tableFrom.status = '';
      this.promoterKeywords = '';
      this.timeVal = [];
      this.getList(1);
    },
    pageChange(page) {
      this.tableFrom.page = page;
      this.getList();
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val;
      this.getList(1);
    },
    handleSearch() {
      this.getList(1);
    },
  },
};
</script>

<style scoped>
.el-icon-arrow-down {
  font-size: 12px;
}

.block {
  padding-bottom: 20px;
  padding-top: 5px;
}
</style>