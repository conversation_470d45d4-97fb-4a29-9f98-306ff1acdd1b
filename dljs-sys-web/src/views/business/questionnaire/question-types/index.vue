<template>
  <div class="survey-editor">
    <el-button @click="$emit('close')" class="action-btn">
      <i class="el-icon-d-arrow-left"></i> 返回
    </el-button>

    <el-button @click="saveSurvey" class="action-btn" :disabled="!isEdit" style="float: right">
      <i class="el-icon-document-add"></i> 保存
    </el-button>

    <el-card class="survey-header">
      <h1>{{ survey.title }}</h1>
      <div v-html="survey.description"></div>
    </el-card>

    <el-card class="survey-show" v-if="isStatistics">
      <span>本次参与填报的次数为:{{modelData.modelQnrCnt}}次</span>
    </el-card>

    <div class="question-list">
      <div v-for="(question, index) in survey.questions" :key="question.id">
        <component
          :is="getQuestionComponent(question.type)"
          :question="question"
          :index="index"
          :is-edit="isEdit"
          :is-statistics="isStatistics"
          :statisticsInfo="statisticsInfo ? statisticsInfo[question.id] : {}"
          @delete="deleteQuestion"
        ></component>
      </div>
    </div>

    <el-button type="primary" @click="showAddQuestion = true" class="add-question-btn" :disabled="!isEdit">
      <i class="el-icon-plus"></i> 添加题目
    </el-button>

    <el-dialog
      title="选择题目类型"
      :visible.sync="showAddQuestion"
      width="60%"
      :before-close="() => showAddQuestion = false"
      custom-class="question-type-dialog"
    >
      <div class="question-type-container">
        <div class="question-type-section">
          <h3 class="section-title">基础题型</h3>
          <div class="question-type-grid">
            <el-button @click="addQuestion('single-choice')" class="type-button" style="margin-left: 14px">
              <i class="el-icon-circle-check"></i>
              <div class="type-info">
                <span class="type-name">单选题</span>
                <span class="type-desc">从多个选项中选择一个答案</span>
              </div>
            </el-button>
            <el-button @click="addQuestion('multiple-choice')" class="type-button">
              <i class="el-icon-check"></i>
              <div class="type-info">
                <span class="type-name">多选题</span>
                <span class="type-desc">从多个选项中选择多个答案</span>
              </div>
            </el-button>
            <el-button @click="addQuestion('dropdown')" class="type-button">
              <i class="el-icon-arrow-down"></i>
              <div class="type-info">
                <span class="type-name">下拉题</span>
                <span class="type-desc">从下拉列表中选择一个答案</span>
              </div>
            </el-button>
            <el-button @click="addQuestion('text')" class="type-button">
              <i class="el-icon-edit"></i>
              <div class="type-info">
                <span class="type-name">问答题</span>
                <span class="type-desc">自由填写文字答案</span>
              </div>
            </el-button>
          </div>
        </div>

        <div class="question-type-section">
          <h3 class="section-title">高级题型</h3>
          <div class="question-type-grid">
            <el-button @click="addQuestion('nps-scale')" class="type-button" style="margin-left: 14px">
              <i class="el-icon-data-line"></i>
              <div class="type-info">
                <span class="type-name">NPS量表</span>
                <span class="type-desc">使用量表进行评分</span>
              </div>
            </el-button>
            <el-button @click="addQuestion('matrix-single')" class="type-button">
              <i class="el-icon-menu"></i>
              <div class="type-info">
                <span class="type-name">矩阵单选题</span>
                <span class="type-desc">多行多列的单选矩阵</span>
              </div>
            </el-button>
            <el-button @click="addQuestion('matrix-multiple')" class="type-button">
              <i class="el-icon-menu"></i>
              <div class="type-info">
                <span class="type-name">矩阵多选题</span>
                <span class="type-desc">多行多列的多选矩阵</span>
              </div>
            </el-button>
            <el-button @click="addQuestion('region-select')" class="type-button">
              <i class="el-icon-location"></i>
              <div class="type-info">
                <span class="type-name">地区选择</span>
                <span class="type-desc">选择省市区信息</span>
              </div>
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {questionnaireItem as api} from '@/utils/api-config';
import * as dataApi from '@/api/business/common';
import {surveyData} from './js/surveyData';
import {cityData} from './js/cityData';
import SingleChoice from './SingleChoice.vue';
import MultipleChoice from './MultipleChoice.vue';
import Dropdown from './Dropdown.vue';
import TextQuestion from './TextQuestion.vue';
import NpsScale from './NpsScale.vue';
import MatrixSingle from './MatrixSingle.vue';
import MatrixMultiple from './MatrixMultiple.vue';
import RegionSelect from './RegionSelect.vue';

export default {
  components: {
    SingleChoice,
    MultipleChoice,
    Dropdown,
    TextQuestion,
    NpsScale,
    MatrixSingle,
    MatrixMultiple,
    RegionSelect,
  },
  props: {
    isEdit: {
      type: Boolean,
      default: false
    },
    isStatistics: {
      type: Boolean,
      default: false
    },
    modelData: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      survey: {
        title: '',
        description: '',
        questions: [],
      },
      showAddQuestion: false,
      statisticsInfo: {},
    }
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.survey.title = this.modelData.modelTitle;
      this.survey.description = this.modelData.modelDesc;
      this.survey.questions = this.dataToModel(this.modelData.itemList);
      if (this.isStatistics && this.modelData.statisticsList && this.modelData.statisticsList.length > 0) {
        let info = {};
        for(let item of this.modelData.statisticsList) {
          info[item.itemId] = item;
        }
        this.statisticsInfo = info;
      }
    },
    // 把模型数据转为题目的结构
    dataToModel(dataList) {
      let modelList = [];
      if (dataList && dataList.length > 0) {
        for (let item of dataList) {
          const model = {
            id: item.id,
            type: item.itemResultType,
            text: item.itemTitle,
            required: item.itemIsRequired === '1',
          }
          switch (item.itemResultType) {
            case 'single-choice':
              model.options = JSON.parse(item.itemResultOption);
              model.answer = null;
              break;
            case 'nps-scale':
              model.scale = JSON.parse(item.itemResultOption);
              model.answer = null;
              break;
            case 'multiple-choice':
              model.options = JSON.parse(item.itemResultOption);
              model.answer = [];
              break;
            case 'dropdown':
              model.options = JSON.parse(item.itemResultOption);
              model.answer = '';
              break;
            case 'text':
              model.answer = '';
              break;
            case 'matrix-single':
              const da = JSON.parse(item.itemResultOption);
              model.rows = da.rows;
              model.cols = da.cols;
              model.answer = { row1: null };
              break;
            case 'matrix-multiple':
              const db = JSON.parse(item.itemResultOption);
              model.rows = db.rows;
              model.cols = db.cols;
              model.answer = { row1: [] };
              break;
            case 'region-select':
              model.regions = JSON.parse(item.itemResultOption);
              model.answer = { country: '', province: '', city: '' };
              break;
            default:

          }
          modelList.push(model);
        }
      }
      return modelList;
    },
    // 把题目的结构转为模型数据
    modelToData(dataList) {
      let modelList = [];
      if (dataList && dataList.length > 0) {
        for (let [index, item] of dataList.entries() ) {
          const model = {
            id: item.id.indexOf('q_') !== -1 ? '' : item.id,
            modelId: this.modelData.id,
            itemTitle: item.text,
            itemSubTitle: '',
            itemParentId: '',
            itemSort: index,
            itemResultType: item.type,
            itemIsRequired: item.required ? '1' : '0',
          }
          switch (item.type) {
            case 'single-choice':
            case 'multiple-choice':
            case 'dropdown':
              model.itemResultOption = JSON.stringify(item.options);
              break;
            case 'region-select':
              model.itemResultOption = JSON.stringify(item.regions);
              break;
            case 'matrix-single':
            case 'matrix-multiple':
              const da = {
                rows: item.rows,
                cols: item.cols
              }
              model.itemResultOption = JSON.stringify(da);
              break;
            case 'nps-scale':
              model.itemResultOption = JSON.stringify(item.scale);
              break;
            case 'text':
              break;
            default:

          }
          modelList.push(model);
        }
      }
      return modelList;
    },
    getQuestionComponent(type) {
      const componentMap = {
        'single-choice': 'SingleChoice',
        'multiple-choice': 'MultipleChoice',
        'dropdown': 'Dropdown',
        'text': 'TextQuestion',
        'nps-scale': 'NpsScale',
        'matrix-single': 'MatrixSingle',
        'matrix-multiple': 'MatrixMultiple',
        'region-select': 'RegionSelect',
      };
      return componentMap[type] || null;
    },
    addQuestion(type) {
      const newQuestionId = 'q_' + Date.now();
      let newQuestion = {
        id: newQuestionId,
        type: type,
        text: '新题目',
        required: false,
        answer: null
      };

      switch (type) {
        case 'single-choice':
          newQuestion.options = [{ value: '1', text: '选项1' }, { value: '2', text: '选项2' }];
          newQuestion.answer = null;
          break;
        case 'multiple-choice':
          newQuestion.options = [{ value: '1', text: '选项1' }, { value: '2', text: '选项2' }];
          newQuestion.answer = [];
          break;
        case 'dropdown':
          newQuestion.options = [{ value: '1', text: '请选择' }, { value: '2', text: '选项1' }, { value: '3', text: '选项2' }];
          newQuestion.answer = '';
          break;
        case 'text':
          newQuestion.answer = '';
          break;
        case 'nps-scale':
          newQuestion.scale = { start: 1, end: 10, startLabel: '非常不可能', endLabel: '非常可能' };
          newQuestion.answer = null;
          break;
        case 'matrix-single':
          newQuestion.rows = [{ value: '1', text: '行项目1' }];
          newQuestion.cols = [{ value: '1', text: '列选项1' }, { value: '2', text: '列选项2' }];
          newQuestion.answer = { row1: null };
          break;
        case 'matrix-multiple':
          newQuestion.rows = [{ value: '1', text: '行项目1' }];
          newQuestion.cols = [{ value: '1', text: '列选项1' }, { value: '2', text: '列选项2' }];
          newQuestion.answer = { row1: [] };
          break;
        case 'region-select':
          newQuestion.regions = cityData;
          newQuestion.answer = { country: '', province: '', city: '' };
          break;
        default:
          console.warn("Unknown question type:", type);
          this.showAddQuestion = false;
          return;
      }
      this.survey.questions.push(newQuestion);
      this.showAddQuestion = false;
    },
    // 删除题目
    deleteQuestion(questionId) {
      this.survey.questions = this.survey.questions.filter(q => q.id !== questionId);
    },
    // 保存题目
    async saveSurvey() {
      if (this.survey.questions.length > 0) {
        let data = this.modelToData(this.survey.questions);
        // 提交数据到后端
        try {
          await dataApi.save(api.path, data);
          this.$message.success(`创建${api.name}成功`);
          this.$emit('close')
        } catch (e) {
          this.$message({
            message: e,
            type: 'warning',
          });
        }
      } else {
        this.$message({
          message: "没有可以提交的题目",
          type: 'warning',
        });
      }
    }
  }
}
</script>

<style scoped>
.action-btn {
  margin-bottom: 15px;
}

.survey-editor {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.survey-header {
  margin-bottom: 30px;
  text-align: center;
}

.survey-header h1 {
  margin-bottom: 10px;
  color: #303133;
}

.survey-header p {
  color: #606266;
  margin-bottom: 0;
}

.survey-show {
  margin-bottom: 30px;
}

.question-list {
  margin-bottom: 20px;
}

.add-question-btn {
  width: 100%;
  margin-top: 20px;
}

.question-type-dialog :deep(.el-dialog__body) {
  padding: 20px 30px;
}

.question-type-container {
  padding: 10px 0;
  width: 100%;
}

.question-type-section {
  margin-bottom: 30px;
  width: 100%;
}

.question-type-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 16px;
  color: #303133;
  margin-bottom: 15px;
  padding-left: 10px;
  border-left: 3px solid #409EFF;
}

.question-type-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  width: 100%;
}

.type-button {
  height: auto !important;
  padding: 15px !important;
  text-align: left !important;
  display: flex !important;
  align-items: flex-start;
  border: 1px solid #DCDFE6;
  transition: all 0.3s;
  width: 100%;
  min-height: 80px;
}

.type-button:hover {
  border-color: #409EFF;
  background-color: #ecf5ff;
}

.type-button i {
  font-size: 24px;
  margin-right: 15px;
  color: #409EFF;
  flex-shrink: 0;
  margin-top: 2px;
}

.type-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex: 1;
  min-width: 0;
}

.type-name {
  font-size: 16px;
  color: #303133;
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

.type-desc {
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}
</style>
