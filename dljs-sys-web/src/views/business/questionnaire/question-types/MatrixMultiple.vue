<template>
  <base-question :question="question" :index="index" :is-edit="isEdit" @delete="$emit('delete', question.id)">
    <div class="matrix-container" v-if="isStatistics">
      <el-table
        :data="tableConfig.tableData"
        border
        style="width: 100%"
        :header-cell-style="{ background: '#ECF5FF', color: '#606266' }">
        <el-table-column
          v-for="col in tableConfig.columns"
          :key="col.prop"
          :prop="col.prop"
          :label="col.label"
          :width="col.width"
          :align="col.align"
          :header-align="col.headerAlign"
        >
        </el-table-column>
      </el-table>
    </div>
    <div class="matrix-container" v-else>
      <div class="top-title" v-if="isEdit">自定义矩阵多选题列名称(使用逗号分隔)：</div>
      <el-input v-model="inputColVal" placeholder="请自定义矩阵多选题列名称" v-if="isEdit">
        <el-button @click="refreshDemo('col')" slot="append" icon="el-icon-refresh-left"></el-button>
      </el-input>
      <div class="top-title" v-if="isEdit">自定义矩阵多选题行名称(使用逗号分隔)：</div>
      <el-input v-model="inputRowVal" placeholder="请自定义矩阵多选题行名称" v-if="isEdit">
        <el-button @click="refreshDemo('row')" slot="append" icon="el-icon-refresh-left"></el-button>
      </el-input>
      <div class="top-title" v-if="isEdit">矩阵多选题示例：</div>
      <el-table :data="rows" border style="width: 100%">
        <el-table-column
          prop="text"
          label=""
          width="180">
        </el-table-column>
        <el-table-column
          v-for="col in cols"
          :key="col.value"
          :label="col.text"
          align="center">
          <template slot-scope="scope">
            <el-checkbox
              v-model="question.answer[scope.row.value]"
              :label="col.text"
              :disabled = "!isEdit"
              @change="handleCheckboxChange(scope.row.value, col.value)"
            ></el-checkbox>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </base-question>
</template>

<script>
import BaseQuestion from './BaseQuestion.vue';

export default {
  components: {
    BaseQuestion
  },
  props: {
    question: {
      type: Object,
      required: true
    },
    index: {
      type: Number,
      required: true
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    isStatistics: {
      type: Boolean,
      default: false
    },
    statisticsInfo: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      rows:[],
      cols:[],
      inputRowVal: '',
      inputColVal: '',
      tableConfig:  {
        columns: [],
        tableData: []
      },
    }
  },
  mounted() {
    if (this.isStatistics) {
      this.transformMatrixToTable({rows: this.question.rows, cols: this.question.cols });
    } else {
      this.initInput();
    }
  },
  created() {
    if (!this.question.answer) {
      this.$set(this.question, 'answer', {});
    }
    this.question.rows.forEach(row => {
      if (this.question.answer[row.value] === undefined || !Array.isArray(this.question.answer[row.value])) {
        this.$set(this.question.answer, row.value, []);
      }
    });
  },
  methods: {
    initInput() {
      if (this.question.cols && this.question.cols.length > 0) {
        // 在可以编辑的时候，需要把text的内容转为input的value，中间使用逗号分隔
        for (let option of this.question.cols) {
          this.inputColVal += option.text + ',';
        }
        this.refreshDemo("col");
      }
      if (this.question.rows && this.question.rows.length > 0) {
        // 在可以编辑的时候，需要把text的内容转为input的value，中间使用逗号分隔
        for (let option of this.question.rows) {
          this.inputRowVal += option.text + ',';
        }
        this.refreshDemo("row");
      }
    },
    refreshDemo(type) {
      if (type === 'col') {
        let invalid = this.inputColVal.replaceAll('，', ',');
        let inArray = invalid.split(',');
        let options = [];
        if (inArray && inArray.length > 0) {
          for (let [index, iv] of inArray.entries()) {
            if (iv) {
              options.push({value: index + 1, text: iv.trim()})
            }
          }
        }
        this.cols = options;
        this.question.cols = options;
      }
      if (type === 'row') {
        let invalid = this.inputRowVal.replaceAll('，', ',');
        let inArray = invalid.split(',');
        let options = [];
        if (inArray && inArray.length > 0) {
          for (let [index, iv] of inArray.entries()) {
            if (iv) {
              options.push({value: index + 1, text: iv.trim()})
            }
          }
        }
        this.rows = options;
        this.question.rows = options;
      }
    },
    handleCheckboxChange(rowValue, colValue) {
      if (!this.question.answer[rowValue]) {
        this.$set(this.question.answer, rowValue, []);
      }
    },

    transformMatrixToTable(matrixData) {
      const { rows, cols } = matrixData;
      const op = {};
      if (this.statisticsInfo) {
        let option = this.statisticsInfo.scResult;
        if (option) {
          let ti = JSON.parse(option);
          for (let opt of ti.count) {
            op[opt.value] = opt;
          }
        }
      }
      // 生成表头数据
      const columns = [
        {
          prop: 'rowName',
          label: '',
        },
        ...cols.map(col => ({
          prop: String(col.value),
          label: col.text,
          align: 'center',
          headerAlign: 'center'
        }))
      ];
      // 生成表格数据
      const tableData = rows.map(row => {
        const rowData = {
          rowName: row.text,
          // rowValue: row.value
        };
        // 为每一列添加默认值
        cols.forEach(col => {
          let key = row.value + '-' + col.value;
          rowData[String(col.value)] = op[key] ? op[key].count : '';
        });
        return rowData;
      });
      this.tableConfig = {
        columns,
        tableData
      };
    }
  }
}
</script>

<style scoped>
:deep(.el-table .cell) {
  text-align: center;
}

:deep(.el-checkbox) {
  margin-right: 0;
}

.top-title {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
  margin: 16px 0 8px;
  padding-left: 8px;
  border-left: 3px solid #409EFF;
  line-height: 1.4;
}

.matrix-container {
  padding: 0 4px;
}
</style>
