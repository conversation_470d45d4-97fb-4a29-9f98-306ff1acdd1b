<template>
  <base-question :question="question" :index="index" :is-edit="isEdit" @delete="$emit('delete', question.id)">
    <div class="dropdown-container" v-if="isStatistics">
      <div>
        <div class="dot-item" v-for="option in options" :key="option.value">
          {{option.text}}
          <span style="color: red;" v-if="statisticsOption[option.value]">{{statisticsOption[option.value].count}} </span>
        </div>
      </div>
    </div>
    <div class="dropdown-container" v-else>
      <div class="top-title" v-if="isEdit">自定义下拉题选项(使用逗号分隔)：</div>
      <el-input v-model="inputVal" placeholder="请自定义下拉题选项" v-if="isEdit">
        <el-button @click="refreshDemo" slot="append" icon="el-icon-refresh-left"></el-button>
      </el-input>
      <div class="top-title" v-if="isEdit">下拉题示例：</div>
      <el-select
        v-model="question.answer"
        placeholder="请选择"
        class="dropdown-select"
        :disabled = "!isEdit"
      >
        <el-option
          v-for="option in options"
          :key="option.value"
          :label="option.text"
          :value="option.value"
        >
        </el-option>
      </el-select>
    </div>
  </base-question>
</template>

<script>
import BaseQuestion from './BaseQuestion.vue';

export default {
  components: {
    BaseQuestion
  },
  props: {
    question: {
      type: Object,
      required: true
    },
    index: {
      type: Number,
      required: true
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    isStatistics: {
      type: Boolean,
      default: false
    },
    statisticsInfo: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      options: [],
      inputVal: '',
      statisticsOption: {}
    }
  },
  mounted() {
    this.initInput();
  },
  methods: {
    initInput() {
      if (this.question.options && this.question.options.length > 0) {
        // 在可以编辑的时候，需要把text的内容转为input的value，中间使用逗号分隔
        for (let option of this.question.options) {
          this.inputVal += option.text + ',';
        }
        this.refreshDemo();
      }
      if (this.isStatistics && this.statisticsInfo) {
        let option = this.statisticsInfo.scResult;
        if (option) {
          let op = {};
          for (let opt of JSON.parse(option)) {
            op[opt.value] = opt;
          }
          this.statisticsOption = op;
        }
      }
    },
    refreshDemo() {
      let invalid = this.inputVal.replaceAll('，', ',');
      let inArray = invalid.split(',');
      let options = [];
      if (inArray && inArray.length > 0) {
        for (let [index, iv] of inArray.entries()) {
          if (iv) {
            options.push({value: index + 1, text: iv.trim()})
          }
        }
      }
      this.options = options;
      this.question.options = options;
    }
  }
}
</script>

<style scoped>
.dropdown-select {
  width: 100%;
}

.top-title {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
  margin: 16px 0 8px;
  padding-left: 8px;
  border-left: 3px solid #409EFF;
  line-height: 1.4;
}

.dropdown-container {
  padding: 0 4px;
}

.dot-item {
  position: relative;
  padding-left: 15px;
  margin-bottom: 10px;
  color: #9ca3af; /* 类似disabled的灰色 */
  cursor: not-allowed; /* 鼠标样式 */
  user-select: none; /* 禁止选中文本 */
}

.dot-item::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #9ca3af; /* 小点颜色与文字一致 */
  font-size: 1.2em;
}
</style>
