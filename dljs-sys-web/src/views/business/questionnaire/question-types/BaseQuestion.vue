<template>
  <el-card class="base-question" shadow="hover">
    <div class="question-header" v-if="isEdit">
      <span class="question-index">{{ index + 1 }}.</span>
      <el-input
        v-model="question.text"
        class="question-text-input"
        placeholder="请输入题目标题"
      ></el-input>
      <div class="question-actions">
        <el-switch
          style="width: 100%"
          v-model="question.required"
          active-color="#409EFF"
          inactive-color="#909399"
          class="required-switch"
        ></el-switch>
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          @click="$emit('delete', question.id)"
          class="delete-button"
        ></el-button>
      </div>
    </div>

    <div class="question-header" v-else>
      <span class="question-index">{{ index + 1 }}.</span>
      {{question.text}}
      <div class="question-actions">
        <span v-if="question.required" style="color: red;font-size: 18px;">*</span>
      </div>
    </div>
    <div class="question-content">
      <slot></slot>  <!-- 题目类型特定的内容会插入这里 -->
    </div>
  </el-card>
</template>

<script>
export default {
  props: {
    question: {
      type: Object,
      required: true
    },
    index: {
      type: Number,
      required: true
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style scoped>
.base-question {
  margin-bottom: 20px;
}

.question-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.question-index {
  font-weight: bold;
  margin-right: 8px;
  color: #303133;
}

.question-text-input {
  flex-grow: 1;
  margin: 0 15px;
}

.question-text-input :deep(.el-input__inner) {
  font-weight: bold;
  font-size: 16px;
}

.question-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  .el-switch .el-switch__core {
    width: 40px !important;
  }
}

.required-switch {
  margin-right: -13px;
}

.required-label {
  font-size: 14px;
  color: #606266;
  margin-right: 15px;
}

.delete-button {
  border: none;
  background-color: #FFF;
  color: #f56464;
  font-size: 17px;
}

.delete-button i {
  margin-right: 4px;
}

.question-content {
  padding: 10px 0;
}
</style>
