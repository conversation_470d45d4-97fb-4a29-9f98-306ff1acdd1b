<template>
  <div>
    <el-form ref="pram" :model="pram" :rules="rules" label-width="100px" @submit.native.prevent>
      <el-form-item label="id：" prop="id" v-show="false">
        <el-input v-model.trim="pram.id" placeholder="id"/>
      </el-form-item>
      <el-form-item label="模板标题：" prop="modelTitle">
        <el-input v-model.trim="pram.modelTitle" placeholder="请输入模板标题"/>
      </el-form-item>
      <el-form-item label="模板状态：" prop="modelState" v-if="isCreate === 1">
        <el-select v-model.trim="pram.modelState" placeholder="请选择模板状态" clearable style="width: 100%;">
          <el-option v-for="item in dictStateList" :key="item.key" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="模板开始时间：" prop="modelStartTime">
            <el-date-picker
              v-model.trim="pram.modelStartTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              class="item-model"
              type="datetime"
              placeholder="选择模板开始时间">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="模板结束时间：" prop="modelEndTime">
            <el-date-picker
              v-model.trim="pram.modelEndTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              class="item-model"
              type="datetime"
              placeholder="选择模板结束时间">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="模板描述：" prop="modelDesc">
        <Tinymce height="200px" v-model="pram.modelDesc" :key="keyIndex"></Tinymce>
      </el-form-item>
      <el-form-item></el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer-inner">
      <el-button @click="close">取消</el-button>
      <el-button
        type="primary"
        @click="handlerSubmit('pram')"
        v-hasPermi="['platform:admin:save', 'platform:admin:update']"
      >
        {{ isCreate === 0 ? '确定' : '更新' }}
      </el-button
      >
    </div>
  </div>
</template>

<script>
// +---------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +---------------------------------------------------------------------
// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
// +---------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +---------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +---------------------------------------------------------------------
import { Debounce } from '@/utils/validate';
import Tinymce from "@/components/Tinymce/index.vue";
import { questionnaire as api } from '@/utils/api-config';
import * as dataApi from '@/api/business/common';

export default {
  components: {Tinymce},
  props: {
    isCreate: {
      type: Number,
      required: true,
    },
    editData: {
      type: Object,
      default: () => {
        return {rules: []};
      },
    },
    dictData: {
      type: Object,
      required: true,
      default: () => {
        return {};
      }
    }
  },
  computed: {
    dictStateList() {
      return this.dictData?.dictState?.itemList || []
    },
  },
  data() {
    return {
      constants: this.$constants,
      pram: {
        id: null,
        modelTitle: null,
        modelStartTime: null,
        modelEndTime: null,
        modelDesc: null,
        modelState: '0',
        modelQnrCnt: 0,
      },
      roleList: [],
      rules: {
        modelTitle: [{required: true, message: '请输入模板标题', trigger: ['blur', 'change']}],
        modelStartTime: [{required: true, message: '请选择模板开始时间', trigger: ['blur', 'change']}],
        modelEndTime: [{required: true, message: '请选择模板结束时间', trigger: ['blur', 'change']}],
        modelDesc: [{required: true, message: '请输入模板描述', trigger: ['blur', 'change']}],
        modelState: [{required: true, message: '请选择模板状态', trigger: ['blur', 'change']}],
      },
      keyIndex: '0',
    };
  },
  mounted() {
    this.initEditData();
  },
  methods: {
    close() {
      this.$emit('hideEditDialog');
    },
    initEditData() {
      if (this.isCreate !== 1) return;
      this.pram = {...this.editData}
    },
    // 提交数据
    handlerSubmit: Debounce(function (form) {
      this.$refs[form].validate((valid) => {
        try {
          if (!valid) throw new Error('请填写完整信息')
          if (this.isCreate === 0) {
            this.handlerSave();
          } else {
            this.handlerEdit();
          }
        } catch (e) {
          this.$message({
            message: e,
            type: 'warning',
          });
        }
      });
    }),
    async handlerSave() {
      await dataApi.save(api.path, this.pram);
      this.$message.success(`创建${api.name}成功`);
      this.$emit('hideEditDialog');
    },
    async handlerEdit() {
      await dataApi.update(api.path, this.pram);
      this.$message.success(`更新${api.name}数据成功`);
      this.$emit('hideEditDialog');
    },
    // 点击商品图
    modalPicTap(multiple) {
      const _this = this;
      const attr = [];
      this.$modalUpload(
        function (img) {
          if (!img) return;
          _this.editPram.icon = img[0].sattDir;
        },
        multiple,
        'store',
      );
    },
  },
};
</script>

<style scoped>
.item-model {
  width: 100%;
}
</style>
