<template>
  <div>
    <el-form ref="pram" :model="pram" :rules="rules" label-width="100px" @submit.native.prevent>
      <el-form-item label="id：" prop="id" v-show="false">
        <el-input v-model.trim="pram.id" placeholder="id"/>
      </el-form-item>
      <el-form-item label="预警标题：" prop="ewTitle">
        <el-input v-model.trim="pram.ewTitle" placeholder="请输入预警标题"/>
      </el-form-item>
      <el-form-item label="预警等级：" prop="ewLevel">
        <el-select v-model.trim="pram.ewLevel" placeholder="请选择预警等级" clearable style="width: 100%;">
          <el-option v-for="item in dictTypeList" :key="item.key" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="预警天数：" prop="ewDay">
            <el-input-number v-model.trim="pram.ewDay" class="item-model" placeholder="请输入预警天数"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="首页弹出显示：" prop="ewIndexShow">
            <el-radio-group v-model.trim="pram.ewIndexShow" class="item-model">
              <el-radio v-for="item in dictYnList" :label="item.value">{{item.label}}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="预警内容：" prop="ewContent">
        <Tinymce height="200px" v-model="pram.ewContent" :key="keyIndex"></Tinymce>
      </el-form-item>
      <el-form-item></el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer-inner">
      <el-button @click="close">取消</el-button>
      <el-button
        type="primary"
        @click="handlerSubmit('pram')"
        v-hasPermi="['platform:admin:save', 'platform:admin:update']"
      >
        {{ isCreate === 0 ? '确定' : '更新' }}
      </el-button
      >
    </div>
  </div>
</template>

<script>
// +---------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +---------------------------------------------------------------------
// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
// +---------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +---------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +---------------------------------------------------------------------
import {Debounce} from '@/utils/validate';
import Tinymce from "@/components/Tinymce/index.vue";
import {warning as api} from '@/utils/api-config';
import * as dataApi from '@/api/business/common';

export default {
  components: {Tinymce},
  props: {
    isCreate: {
      type: Number,
      required: true,
    },
    editData: {
      type: Object,
      default: () => {
        return {rules: []};
      },
    },
    dictData: {
      type: Object,
      required: true,
      default: () => {
        return {};
      }
    }
  },
  computed: {
    dictTypeList() {
      return this.dictData?.dictType?.itemList || []
    },
    dictYnList() {
      return this.dictData?.dictYn?.itemList || []
    },
  },
  data() {
    return {
      constants: this.$constants,
      pram: {
        id: null,
        ewLevel: null,
        ewTitle: null,
        ewDay: 10,
        ewContent: null,
        ewState: null,
        ewIndexShow: '0',
      },
      roleList: [],
      rules: {
        ewLevel: [{required: true, message: '预警等级', trigger: ['blur', 'change']}],
        ewTitle: [{required: true, message: '预警标题', trigger: ['blur', 'change']}],
        ewDay: [{required: true, message: '预警天数', trigger: ['blur', 'change']}],
        ewContent: [{required: true, message: '预警内容', trigger: ['blur', 'change']}],
        ewState: [{required: true, message: '预警状态', trigger: ['blur', 'change']}],
        ewIndexShow: [{required: true, message: '首页弹出显示', trigger: ['blur', 'change']}],
      },
      keyIndex: '0',
    };
  },
  mounted() {
    this.initEditData();
  },
  methods: {
    close() {
      this.$emit('hideEditDialog');
    },
    initEditData() {
      if (this.isCreate !== 1) return;
      this.pram = {...this.editData}
    },
    // 提交数据
    handlerSubmit: Debounce(function (form) {
      this.$refs[form].validate((valid) => {
        try {
          if (!valid) throw new Error('请填写完整信息')
          if (this.isCreate === 0) {
            this.handlerSave();
          } else {
            this.handlerEdit();
          }
        } catch (e) {
          this.$message({
            message: e,
            type: 'warning',
          });
        }
      });
    }),
    async handlerSave() {
      await dataApi.save(api.path, this.pram);
      this.$message.success(`创建${api.name}成功`);
      this.$emit('hideEditDialog');
    },
    async handlerEdit() {
      await dataApi.update(api.path, this.pram);
      this.$message.success(`更新${api.name}数据成功`);
      this.$emit('hideEditDialog');
    },
    // 点击商品图
    modalPicTap(multiple) {
      const _this = this;
      const attr = [];
      this.$modalUpload(
        function (img) {
          if (!img) return;
          _this.editPram.icon = img[0].sattDir;
        },
        multiple,
        'store',
      );
    },
  },
};
</script>

<style scoped>
.item-model {
  width: 100%;
}
</style>
