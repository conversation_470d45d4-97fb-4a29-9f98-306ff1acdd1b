<template>
  <div>
    <el-form ref="pram" :model="pram" :rules="rules" label-width="100px" @submit.native.prevent>
      <el-form-item label="id：" prop="id" v-show="false">
        <el-input v-model.trim="pram.id" placeholder="id"/>
      </el-form-item>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="投诉人员：" prop="cmUser">
            <el-input v-model.trim="pram.cmUser" disabled class="item-model" placeholder="请输入投诉人员"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话：" prop="cmUserPhone">
            <el-input v-model.trim="pram.cmUserPhone" disabled class="item-model" placeholder="请输入联系电话"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="投诉类型：" prop="cmType">
            <el-select v-model.trim="pram.cmType" disabled placeholder="请选择投诉类型" clearable style="width: 100%;">
              <el-option v-for="item in dataConfigList" :key="item.key" :label="item.label" :value="item.value"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="投诉时间：" prop="cmTime">
            <el-input v-model.trim="pram.cmTime" disabled placeholder="请输入投诉时间"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="投诉内容：" prop="cmContent">
        <el-input type="textarea" :rows="8" v-model.trim="pram.cmContent" disabled placeholder="请输入投诉内容"/>
      </el-form-item>
      <el-form-item label="投诉照片：" prop="cmImg">
        <div class="upLoadPicBox">
          <div v-if="pram.cmImg">
            <div v-for="item in getImgUrl(pram.cmImg)" class="pictrue">
              <img :src="item" alt=""/>
            </div>
          </div>
          <div v-else class="upLoad">
            <i class="el-icon-camera cameraIconfont"/>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="回复内容：" prop="cmReplyContent">
        <el-input type="textarea" :disabled="isDisable" :rows="8" v-model.trim="pram.cmReplyContent" placeholder="请输入回复内容"/>
      </el-form-item>
      <el-form-item></el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer-inner">
      <el-button @click="close">取消</el-button>
      <el-button
        v-if="!isDisable"
        type="primary"
        @click="handlerSubmit('pram')"
        v-hasPermi="['platform:admin:save', 'platform:admin:update']"
      >
        {{ isCreate === 0 ? '确定' : '更新' }}
      </el-button
      >
    </div>
  </div>
</template>

<script>
// +---------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +---------------------------------------------------------------------
// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
// +---------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +---------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +---------------------------------------------------------------------
import { Debounce } from '@/utils/validate';
import Tinymce from "@/components/Tinymce/index.vue";
import { complaints as api } from '@/utils/api-config';
import * as dataApi from '@/api/business/common';
import {getImgUrl} from "@/utils/imgUtil";

export default {
  components: { Tinymce },
  props: {
    isCreate: {
      type: Number,
      required: true,
    },
    editData: {
      type: Object,
      default: () => {
        return {rules: []};
      },
    },
    dictData: {
      type: Object,
      required: true,
      default: () => {
        return {};
      }
    }
  },
  computed: {
    dataConfigList() {
      return this.dictData?.dataConfig?.itemList || []
    }
  },
  data() {
    return {
      constants: this.$constants,
      pram: {
        cmUser: null,
        cmUserPhone: null,
        cmContent: null,
        cmType: null,
        cmImg: null,
        cmState: null,
        cmReplyContent: null,
        cmTime: null,
        cmReplyTime: null,
        id: null,
      },
      roleList: [],
      rules: {
        cmReplyContent: [{required: true, message: '请输入回复内容', trigger: ['blur', 'change']}],
      },
      keyIndex: '0',
      isDisable: false,
    };
  },
  mounted() {
    this.initEditData();
  },
  methods: {
    getImgUrl,
    close() {
      this.$emit('hideEditDialog');
    },
    initEditData() {
      if (this.isCreate === 0) return;
      if (this.isCreate === 2) this.isDisable  = true;
      this.pram = {...this.editData}
    },
    // 提交数据
    handlerSubmit: Debounce(function (form) {
      this.$refs[form].validate((valid) => {
        try {
          if (!valid) throw new Error('请填写完整信息')
          if (this.isCreate === 0) {
            this.handlerSave();
          } else {
            this.handlerEdit();
          }
        } catch (e) {
          this.$message({
            message: e,
            type: 'warning',
          });
        }
      });
    }),
    async handlerSave() {
      await dataApi.save(api.path, this.pram);
      this.$message.success(`创建${api.name}成功`);
      this.$emit('hideEditDialog');
    },
    async handlerEdit() {
      await dataApi.update(api.path, this.pram);
      this.$message.success(`更新${api.name}数据成功`);
      this.$emit('hideEditDialog');
    },
    // 点击商品图
    modalPicTap(multiple) {
      const _this = this;
      const attr = [];
      this.$modalUpload(
        function (img) {
          if (!img) return;
          _this.editPram.icon = img[0].sattDir;
        },
        multiple,
        'store',
      );
    },
  },
};
</script>

<style scoped>
.item-model {
  width: 100%;
}
.pictrue {
  float: left;
}
</style>
