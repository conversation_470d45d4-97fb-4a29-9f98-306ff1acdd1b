<template>
  <div>
    <el-form ref="pram" :model="pram" :rules="rules" label-width="100px" @submit.native.prevent>
        <el-form-item label="id：" prop="id" v-show="false">
        <el-input v-model.trim="pram.id" placeholder="id"/>
      </el-form-item>
      <el-form-item label="随机分配热点" prop="integralRandomHotspot">
        <el-select
          v-model.trim="pram.integralRandomHotspot"
          multiple
          placeholder="请选择随机分配热点"
          clearable
          style="width: 100%"
        >
          <el-option v-for="item in dataConfigList" :key="item.key" :label="item.text" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="固定分配热点" prop="integralFixedHotspot">
        <el-select
          v-model.trim="pram.integralFixedHotspot"
          multiple
          placeholder="请选择固定分配热点"
          clearable
          style="width: 100%"
        >
          <el-option v-for="item in dataConfigList" :key="item.key" :label="item.text" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="随机数量(个/天)" prop="integralRandomNumber">
            <el-input-number v-model.trim="pram.integralRandomNumber" class="item-model" placeholder="请输入分配数量" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="固定数量(个/天)" prop="integralFixedNumber" v-if="false">
            <el-input-number v-model.trim="pram.integralFixedNumber" class="item-model" placeholder="请输入分配数量" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="问卷奖励(个)" prop="integralSurvey">
            <el-input-number v-model.trim="pram.integralSurvey" class="item-model" placeholder="请输入问卷调查奖励" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="消费奖励(%)" prop="integralConsume">
            <el-input-number v-model.trim="pram.integralConsume" class="item-model" placeholder="请输入消费奖励" />
          </el-form-item>
        </el-col>
      </el-row>
      <!--      <el-row :gutter="24">-->
      <!--        <el-col :span="12">-->
      <!--          <el-form-item label="龙宫币规则" prop="integralRuleImg">-->
      <!--            <el-input v-model.trim="pram.integralRuleImg" class="item-model" placeholder="请输入龙宫币规则" />-->
      <!--          </el-form-item>-->
      <!--        </el-col>-->
      <!--        <el-col :span="12">-->
      <!--          <el-form-item label="兑换规则" prop="integralConvertImg">-->
      <!--            <el-input v-model.trim="pram.integralConvertImg" class="item-model" placeholder="请输入兑换规则" />-->
      <!--          </el-form-item>-->
      <!--        </el-col>-->
      <!--      </el-row>-->
      <!-- <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="是否启用" prop="integralEnable">
            <el-radio-group v-model.trim="pram.integralEnable" class="item-model">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row> -->
      <el-form-item label="描述：" prop="informDesc">
        <el-input type="textarea" v-model.trim="pram.informDesc" placeholder="请输入资讯描述" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer-inner">
      <el-button @click="close">取消</el-button>
      <el-button
        type="primary"
        @click="handlerSubmit('pram')"
        v-hasPermi="['platform:admin:save', 'platform:admin:update']"
      >
        {{ isCreate === 0 ? '确定' : '更新' }}
      </el-button>
    </div>
  </div>
</template>

<script>
// +---------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +---------------------------------------------------------------------
// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
// +---------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +---------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +---------------------------------------------------------------------
import { Debounce } from '@/utils/validate';
import * as dataApi from '@/api/business/integralConfigMgmt.js';

export default {
  props: {
    isCreate: {
      type: Number,
      required: true,
    },
    editData: {
      type: Object,
      default: () => {
        return { rules: [] };
      },
    },
    dictData: {
      type: Object,
      required: true,
      default: () => {
        return {};
      },
    },
  },
  computed: {
    dataConfigList() {
      return this.dictData?.dataConfig?.itemList || [];
    },
  },
  data() {
    return {
      constants: this.$constants,
      pram: {
        id: null,
        integralEnable: false,
        integralRandomHotspot: null,
        integralRandomNumber: null,
        integralFixedHotspot: null,
        integralFixedNumber: 1,
        integralSurvey: null,
        integralConsume: null,
        integralRuleImg: null,
        integralConvertImg: null,
        integralDesc: null,
      },
      roleList: [],
      rules: {
        integralRandomHotspot: [{ required: true, message: '请选择随机分配热点', trigger: ['blur', 'change'] }],
        integralRandomNumber: [{ required: true, message: '请输入随机分配数量', trigger: ['blur', 'change'] }],
        integralFixedHotspot: [{ required: true, message: '请选择固定分配热点', trigger: ['blur', 'change'] }],
        integralFixedNumber: [{ required: true, message: '请输入固定分配数量', trigger: ['blur', 'change'] }],
        integralSurvey: [{ required: true, message: '请输入问卷调查奖励', trigger: ['blur', 'change'] }],
        integralConsume: [{ required: true, message: '请输入消费奖励', trigger: ['blur', 'change'] }],
      },
      keyIndex: '0',
    };
  },
  mounted() {
    this.initEditData();
  },
  methods: {
    close() {
      this.$emit('hideEditDialog');
    },
    initEditData() {
      if (this.isCreate !== 1) return;
      this.pram = {
        ...this.editData,
        integralRandomHotspot: this.editData.integralRandomHotspot.split(','),
        integralFixedHotspot: this.editData.integralFixedHotspot.split(','),
      };
    },
    // 提交数据
    handlerSubmit: Debounce(function (form) {
      this.$refs[form].validate((valid) => {
        if (!valid) return;
        if (this.isCreate === 0) {
          this.handlerSave();
        } else {
          this.handlerEdit();
        }
      });
    }),
    async handlerSave() {
      this.pram.integralFixedHotspot = this.pram.integralFixedHotspot?.join(',');
      this.pram.integralRandomHotspot = this.pram.integralRandomHotspot?.join(',');
      await dataApi.save(this.pram);
      this.$message.success('新增成功');
      this.$emit('hideEditDialog');
    },
    async handlerEdit() {
      this.pram.integralFixedHotspot = this.pram.integralFixedHotspot?.join(',');
      this.pram.integralRandomHotspot = this.pram.integralRandomHotspot?.join(',');
      await dataApi.update(this.pram);
      this.$message.success('修改成功');
      this.$emit('hideEditDialog');
    },
  },
};
</script>

<style scoped>
.item-model {
  width: 100%;
}
</style>
