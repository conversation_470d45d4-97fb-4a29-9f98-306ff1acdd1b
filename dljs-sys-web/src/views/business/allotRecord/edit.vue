<template>
    <div>
      <el-form ref="pram" :model="pram" :rules="rules" label-width="100px" @submit.native.prevent>
        <el-form-item label="id：" prop="id" v-show="false">
          <el-input v-model.trim="pram.id" placeholder="id"/>
        </el-form-item>
        <el-form-item label="随机分配数量" prop="allotRandomNumber">
          <el-input-number style="width: 100%;" v-model.trim="pram.allotRandomNumber" placeholder="请输入分配数量"/>
        </el-form-item>
 
      </el-form>
      <div slot="footer" class="dialog-footer-inner">
        <el-button @click="close">取消</el-button>
        <el-button
          type="primary"
          @click="handlerSubmit('pram')"
          v-hasPermi="['platform:admin:save', 'platform:admin:update']"
        >
          {{ isCreate === 0 ? '确定' : '更新' }}
        </el-button
        >
      </div>
    </div>
  </template>
  
  <script>
  // +---------------------------------------------------------------------
  // | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
  // +---------------------------------------------------------------------
  // | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
  // +---------------------------------------------------------------------
  // | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
  // +---------------------------------------------------------------------
  // | Author: CRMEB Team <<EMAIL>>
  // +---------------------------------------------------------------------
  import {Debounce} from '@/utils/validate';
  import {record as api} from '@/utils/api-config';
  import * as dataApi from '@/api/business/common';
  
  export default {
    props: {
      isCreate: {
        type: Number,
        required: true,
      },
      editData: {
        type: Object,
        default: () => {
          return {rules: []};
        },
      },
      dictData: {
        type: Object,
        required: true,
        default: () => {
          return {};
        }
      }
    },
    computed: {

    },
    data() {
      return {
        constants: this.$constants,
        pram: {
          id: null,
          allotRandomNumber: null,
        },
        roleList: [],
        rules: {
          
        },
        keyIndex: '0',
      };
    },
    mounted() {
      this.initEditData();
    },
    methods: {
      close() {
        this.$emit('hideEditDialog');
      },
      initEditData() {
        if (this.isCreate !== 1) return;
        this.pram = {...this.editData}
      },
      // 提交数据
      handlerSubmit: Debounce(function (form) {
        this.$refs[form].validate((valid) => {
        if (!valid) return;
        if (this.isCreate === 0) {
          this.handlerSave();
        } else {
          this.handlerEdit();
        }
      });
      }),
      async handlerSave() {
        await dataApi.save(api.path, this.pram);
        this.$message.success(`创建${api.name}成功`);
        this.$emit('hideEditDialog');
      },
      async handlerEdit() {
        await dataApi.update(api.path, this.pram);
        this.$message.success(`更新${api.name}数据成功`);
        this.$emit('hideEditDialog');
      },
    },
  };
  </script>
  
  <style scoped>
  .item-model {
    width: 100%;
  }
  </style>
  