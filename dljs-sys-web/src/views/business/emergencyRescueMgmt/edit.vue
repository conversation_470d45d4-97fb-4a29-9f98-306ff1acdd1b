<template>
  <div>
    <el-form ref="pram" :model="pram" :rules="rules" label-width="100px" @submit.native.prevent>
      <el-form-item label="id：" prop="id" v-show="false">
        <el-input v-model.trim="pram.id" placeholder="id"/>
      </el-form-item>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="序号：" prop="index">
          <el-input-number
            v-model.trim="pram.index" placeholder="请输入序号"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="名称：" prop="name">
            <el-input v-model.trim="pram.name" class="item-model" placeholder="请输入名称"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="内容：" prop="content">
        <Tinymce height="200px" v-model="pram.content" :key="keyIndex"></Tinymce>
      </el-form-item>
      <el-form-item></el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer-inner">
      <el-button @click="close">取消</el-button>
      <el-button
        type="primary"
        @click="handlerSubmit('pram')"
        v-hasPermi="['platform:admin:save', 'platform:admin:update']"
      >
        {{ isCreate === 0 ? '确定' : '更新' }}
      </el-button
      >
    </div>
  </div>
</template>

<script>
// +---------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +---------------------------------------------------------------------
// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
// +---------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +---------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +---------------------------------------------------------------------
import {Debounce} from '@/utils/validate';
import Tinymce from "@/components/Tinymce/index.vue";

export default {
  components: {Tinymce},
  props: {
    isCreate: {
      type: Number,
      required: true,
    },
    editData: {
      type: Object,
      default: () => {
        return {rules: []};
      },
    },
    dictData: {
      type: Object,
      required: true,
      default: () => {
        return {};
      }
    }
  },
  computed: {
  },
  data() {
    return {
      constants: this.$constants,
      pram: {
        id: Date.now(),
        index: 1,
        name: null,
        content: null,
        type: 1, // 1、新增 2、编辑
      },
      roleList: [],
      rules: {
        index: [{required: true, message: '请输入序号', trigger: ['blur', 'change']}],
        name: [{required: true, message: '请输入名称', trigger: ['blur', 'change']}],
        content: [{required: true, message: '请输入内容', trigger: ['blur', 'change']}],
      },
      keyIndex: '0',
    };
  },
  mounted() {
    this.initEditData();
  },
  methods: {
    close() {
      this.$emit('hideEditDialog');
    },
    initEditData() {
      if (this.isCreate !== 1) return;
      this.pram = {...this.editData, type: 2}
    },
    // 提交数据
    handlerSubmit: Debounce(function (form) {
      this.$refs[form].validate((valid) => {
        try {
          if (!valid) throw new Error('请填写完整信息')
          this.$emit('hideEditDialog', this.pram);
        } catch (e) {
          this.$message({
            message: e,
            type: 'warning',
          });
        }
      });
    }),
  },
};
</script>

<style scoped>
.item-model {
  width: 100%;
}
</style>
