<template>
  <div>
    <el-form ref="pram" :model="pram" :rules="rules" label-width="100px" @submit.native.prevent>
      <el-form-item label="id：" prop="id" v-show="false">
        <el-input v-model.trim="pram.id" placeholder="id" />
      </el-form-item>
      <el-form-item label="攻略名称：" prop="guideTitle">
        <el-input v-model.trim="pram.guideTitle" placeholder="请输入攻略名称" />
      </el-form-item>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="攻略作者：" prop="guideAuthor">
            <el-input v-model.trim="pram.guideAuthor" class="item-model" placeholder="请输入攻略作者" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="攻略排序：" prop="guideTop">
            <el-input-number v-model.trim="pram.guideTop" class="item-model" placeholder="请输入攻略排序" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="攻略发布时间：" prop="guidePushTime">
            <el-date-picker
              v-model.trim="pram.guidePushTime"
              class="item-model"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择发布时间"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否首页显示：" prop="guideIndexShow">
            <el-radio-group v-model.trim="pram.guideIndexShow" class="item-model">
              <el-radio v-for="item in dictYnList" :key="item.key" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="攻略类型：" prop="guideType">
            <el-select v-model.trim="pram.guideType" placeholder="请选择攻略类型" clearable style="width: 100%">
              <el-option v-for="item in dataConfigList" :key="item.key" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="攻略来源：" prop="guideDataSource">
            <el-input v-model.trim="pram.guideDataSource" placeholder="请输入攻略来源" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 当攻略类型是"吃在金梭"(eat)或"住在金梭"(live)时显示 -->
      <template v-if="pram.guideType === 'eat' || pram.guideType === 'live'">
        <el-form-item label="联系电话：" prop="telephone">
          <el-input v-model.trim="pram.telephone" placeholder="多个电话请用逗号分隔" />
        </el-form-item>
        <el-form-item label="选择位置:" prop="address">
          <div style="width: 100px;">
            <el-button @click="showMapPicker">选择位置</el-button>
          </div>
        </el-form-item>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="经度：" prop="longitude">
              <el-input disabled v-model.trim="pram.longitude" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="纬度：" prop="latitude">
              <el-input disabled v-model.trim="pram.latitude" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="位置：" prop="address">
          <el-input v-model.trim="pram.address" placeholder="请输入位置" />
        </el-form-item>
      </template>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="攻略封面图：" prop="guideCover">
            <div class="upLoadPicBox" @click="modalPicTap(false)">
              <div v-if="pram.guideCover" class="pictrue">
                <img :src="getImgUrl(pram.guideCover)[0]" alt="" />
              </div>
              <div v-else class="upLoad">
                <i class="el-icon-camera cameraIconfont" />
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="相册：" prop="img">
        <div style="border: 1px solid #DCDFE6; padding: 20px;">
          <el-tabs v-model="editableTabsValue" type="card" editable @edit="handleTabsEdit">
            <el-tab-pane 
              v-for="item in editableTabs" 
              :key="item.name" 
              :label="item.title" 
              :name="item.name">
            </el-tab-pane>
          </el-tabs>

          <div>
            <span style="font-size: 12px">标签：</span>
            <el-input
              v-model="inp[editableTabsValue]"
              class="item-model"
              @blur="handleBlur(editableTabsValue)"
              placeholder="请输入相册标签"
            />
            <span style="font-size: 12px">图片：</span>
            <div class="image-container" style="display: flex; flex-wrap: wrap; margin-top: 10px;">
              <!-- 已有图片区域 -->
              <template v-if="checkPicList[editableTabsValue]">
                <div 
                  v-for="(item, index) in getImgUrl(checkPicList[editableTabsValue])" 
                  :key="index"
                  class="image-item"
                  style="position: relative; width: 58px; height: 58px; margin-right: 10px; margin-bottom: 10px;"
                >
                  <img 
                    :src="item" 
                    alt="" 
                    style="width: 100%; height: 100%; object-fit: cover; cursor: pointer;" 
                    @click="previewImage(item)"
                  />
                  <div 
                    class="delete-icon" 
                    style="position: absolute; top: -8px; right: -8px; background-color: #f56c6c; color: white; width: 18px; height: 18px; border-radius: 50%; display: flex; justify-content: center; align-items: center; cursor: pointer;"
                    @click.stop="deleteImage(editableTabsValue, index)"
                  >
                    <i class="el-icon-close" style="font-size: 12px;"></i>
                  </div>
                </div>
              </template>
              
              <!-- 添加按钮 -->
              <div 
                class="upLoad"
                style="width: 58px; height: 58px; display: flex; justify-content: center; align-items: center; border: 1px dashed #d9d9d9; cursor: pointer;"
                @click="modalPicTap1(true, editableTabsValue)"
              >
                <i class="el-icon-camera cameraIconfont" />
              </div>
            </div>
          </div>
        </div>
      </el-form-item>

      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="视频：" prop="video">
            <div class="video-container" style="display: flex; flex-wrap: wrap;">
              <!-- 已有视频区域 -->
              <template v-if="videolist">
                <div 
                  v-for="(item, index) in getImgUrl(videolist)" 
                  :key="index"
                  class="video-item"
                  style="position: relative; width: 80px; height: 60px; margin-right: 10px; margin-bottom: 10px;"
                >
                  <video 
                    :src="item" 
                    style="width: 100%; height: 100%; object-fit: cover; cursor: pointer;" 
                    @click="previewVideo(item)"
                  ></video>
                  <div 
                    class="delete-icon" 
                    style="position: absolute; top: -8px; right: -8px; background-color: #f56c6c; color: white; width: 18px; height: 18px; border-radius: 50%; display: flex; justify-content: center; align-items: center; cursor: pointer;"
                    @click.stop="deleteVideo(index)"
                  >
                    <i class="el-icon-close" style="font-size: 12px;"></i>
                  </div>
                </div>
              </template>
              
              <!-- 添加按钮 -->
              <div 
                class="upLoad"
                style="width: 80px; height: 60px; display: flex; justify-content: center; align-items: center; border: 1px dashed #d9d9d9; cursor: pointer;"
                @click="modalPicTap2(true)"
              >
                <i class="el-icon-camera cameraIconfont" />
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="攻略描述：" prop="guideDesc">
        <el-input type="textarea" v-model.trim="pram.guideDesc" placeholder="请输入攻略描述" />
      </el-form-item>
      <el-form-item label="攻略内容：" prop="guideContent">
        <Tinymce height="200px" v-model="pram.guideContent" :key="keyIndex"></Tinymce>
      </el-form-item>
      <el-form-item></el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer-inner">
      <el-button @click="close">取消</el-button>
      <el-button
        type="primary"
        @click="handlerSubmit('pram')"
        v-hasPermi="['platform:admin:save', 'platform:admin:update']"
      >
        {{ isCreate === 0 ? '确定' : '更新' }}
      </el-button>
    </div>
  </div>
</template>

<script>
// +---------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +---------------------------------------------------------------------
// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
// +---------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +---------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +---------------------------------------------------------------------
import { Debounce } from '@/utils/validate';
import Tinymce from '@/components/Tinymce/index.vue';
import { guide as api } from '@/utils/api-config';
import * as dataApi from '@/api/business/common';
import { getImgUrl } from '@/utils/imgUtil';
import TencentMapPicker from '@/components/base/TencentMapPicker.vue';
export default {
  components: {Tinymce, TencentMapPicker },
  props: {
    isCreate: {
      type: Number,
      required: true,
    },
    editData: {
      type: Object,
      default: () => {
        return { rules: [] };
      },
    },
    dictData: {
      type: Object,
      required: true,
      default: () => {
        return {};
      },
    },
  },
  computed: {
    dataConfigList() {
      return this.dictData?.dataConfig?.itemList || [];
    },
    dictYnList() {
      return this.dictData?.dictYn?.itemList || [];
    },
  },
  data() {
    return {
      mapVisible: false,
      defaultLng: 100.257298,
      defaultLat: 25.695042,
      selectedLocation: null,
      constants: this.$constants,
      pram: {
        id: null,
        guideType: null,
        guideDataSource: null,
        guideCover: null,
        guideTitle: null,
        guideAuthor: null,
        guideTop: 0,
        guidePushTime: new Date(),
        guideIndexShow: '0',
        guideDesc: null,
        guideContent: null,
        telephone: null,
        longitude:null,
        latitude:null,
        address:null,
        imgUrl: null,
        video: null,
      },
      roleList: [],
      rules: {
        guideType: [{ required: true, message: '请选择攻略类型', trigger: ['blur', 'change'] }],
        guideCover: [{ required: true, message: '请上传攻略封面图', trigger: ['blur', 'change'] }],
        guideTitle: [{ required: true, message: '请输入攻略名称', trigger: ['blur', 'change'] }],
        guideAuthor: [{ required: true, message: '请输入攻略作者', trigger: ['blur', 'change'] }],
        guideTop: [{ required: true, message: '请输入攻略排序', trigger: ['blur', 'change'] }],
        guidePushTime: [{ required: true, message: '请选择攻略发布时间', trigger: ['blur', 'change'] }],
        guideIndexShow: [{ required: true, message: '请选择是否首页显示', trigger: ['blur', 'change'] }],
        guideContent: [{ required: true, message: '请输入攻略内容', trigger: ['blur', 'change'] }],
      },
      keyIndex: '0',

      editableTabsValue: '1',
      editableTabs: [
        {
          title: '1',
          name: '1',
          content: '',
        },
      ],
      tabIndex: 1,
      inp: {},//'1':'1111'
      checkPicList: {},//'1':'temp/1747980475341_1747980475573.png'
      videolist: '',
      videos: '',
    };
  },
  mounted() {
    this.initEditData();
  },
  methods: {
    showMapPicker() {
      this.$emit('openMap', true);
    },
    handleLocationConfirm(location) {
      this.selectedLocation = location;
      this.pram.longitude = location.lng;
      this.pram.latitude = location.lat;
      this.$emit('openMap', false);
    },
    getImgUrl,
    close() {
      this.$emit('hideEditDialog');
    },
    initEditData() {
      if (this.isCreate !== 1) return;
      dataApi.info(api.path, this.editData.id).then((res) => {
        this.pram = res;
        this.videolist = res.video;
        // res.attachmentList
        if (res && res.attachmentList && res.attachmentList.length > 0) {

          let inp = {}
          let checkPicList = {}
          let editableTabs = []
          for (let li of res.attachmentList) {
            checkPicList = {[li.sort]: li.imgUrl, ...checkPicList};
            inp = {[li.sort]: li.tagsType,...inp}
            editableTabs.unshift({
              title: li.tagsType,
              name: li.sort,
              content: li.imgUrl,
            });
          }
          this.editableTabs = editableTabs;
          this.inp = inp
          this.checkPicList = checkPicList
        }
      });

      // this.pram = { ...this.editData };
    },
    // 提交数据
    handlerSubmit: Debounce(function (form) {
      this.$refs[form].validate((valid) => {
        try {
          if (!valid) throw new Error('请填写完整信息');
          if (this.isCreate === 0) {
            this.handlerSave();
          } else {
            this.handlerEdit();
          }
        } catch (e) {
          this.$message({
            message: e,
            type: 'warning',
          });
        }
      });
    }),
    async handlerSave() {
      this.pram.imgUrl = JSON.stringify(this.editableTabs);
      this.pram.video = this.videolist;
      await dataApi.save(api.path, this.pram);
      this.$message.success(`创建${api.name}成功`);
      this.$emit('hideEditDialog');
    },
    async handlerEdit() {

      this.pram.imgUrl = JSON.stringify(this.editableTabs);
      this.pram.video = this.videolist;
      await dataApi.update(api.path, this.pram);
      this.$message.success(`更新${api.name}数据成功`);
      this.$emit('hideEditDialog');
    },
    // 点击商品图
    modalPicTap(multiple) {
      const _this = this;
      const attr = [];
      this.$modalUpload(
        function (img) {
          if (!img) return;
          _this.pram.guideCover = img[0].sattDir;
        },
        multiple,
        'store',
      );
    },

    modalPicTap2(multiple) {
      const _this = this;
      const attr = [];
      this.$modalUpload(
        function (img) {
          if (!img) return;
          let video = '';
          for (let item of img) {
            video += item.sattDir + ',';
          }
          _this.videolist = video.substring(0, video.length - 1);
          console.log(JSON.stringify(_this.videolist));
        },
        multiple,
        'store',
      );
    },

    modalPicTap1(multiple, sid) {
      const _this = this;
      const attr = [];
      this.$modalUpload(
        function (img) {
          if (!img) return;
          if (img && img.length > 0) {
            let im = '';
            for (let item of img) {
              im += item.sattDir + ',';
            }
            let simg = _this.checkPicList[sid];
            const result = {
              ..._this.checkPicList,
              [sid]: simg ? simg + ',' + im.substring(0, im.length - 1) : im.substring(0, im.length - 1),
            };
            _this.checkPicList = result;
            for (let tab of _this.editableTabs) {
              if (sid === tab.name) {
                const newTitle = _this.checkPicList[sid];
                tab['content'] = newTitle;
              }
            }
          }
        },
        multiple,
        'store',
      );
    },

    handleBlur(sid) {
      for (let tab of this.editableTabs) {
        if (sid === tab.name) {
          const newTitle = this.inp[sid];
          tab['title'] = newTitle;
        }
      }
    },

    handleTabsEdit(targetName, action) {
      if (action === 'add') {
        let newTabName = ++this.tabIndex + '';
        this.editableTabs.push({
          title: newTabName + '.',
          name: newTabName,
          content: 'New Tab content',
        });
        this.editableTabsValue = newTabName;
      }
      if (action === 'remove') {
        let tabs = this.editableTabs;
        let activeName = this.editableTabsValue;
        if (activeName === targetName) {
          tabs.forEach((tab, index) => {
            if (tab.name === targetName) {
              let nextTab = tabs[index + 1] || tabs[index - 1];
              if (nextTab) {
                activeName = nextTab.name;
              }
            }
          });
        }

        this.editableTabsValue = activeName;
        this.editableTabs = tabs.filter((tab) => tab.name !== targetName);
      }
    },

    previewImage(src) {
      // 实现图片预览功能，使用Element UI的方式
      // 创建一个临时的dialog来预览图片
      this.$alert(
        `<img style="width: 100%;" src="${src}" />`,
        '图片预览',
        {
          dangerouslyUseHTMLString: true,
          showConfirmButton: false,
          callback: () => {}
        }
      );
    },

    deleteImage(sid, index) {
      // 实现删除图片功能
      if (this.checkPicList[sid]) {
        const imagePaths = this.checkPicList[sid].split(',');
        
        // 显示确认对话框
        // this.$modalSure('是否确认删除该图片？').then(() => {
          try {
            // 直接从数组中移除该图片
            const newImagePaths = imagePaths.filter((_, i) => i !== index);
            const newImageStr = newImagePaths.join(',');
            
            // 更新数据
            this.$set(this.checkPicList, sid, newImageStr);
            
            // 更新tabs中的content
            for (let tab of this.editableTabs) {
              if (sid === tab.name) {
                tab.content = newImageStr;
              }
            }
            
            this.$message.success('删除图片成功');
          } catch (err) {
            console.error('删除图片出错:', err);
            this.$message.error('删除图片出错：' + (err.message || '未知错误'));
          }
        // }).catch(() => {
          // 用户取消删除操作
          // this.$message.info('已取消删除');
        // });
      }
    },

    previewVideo(src) {
      // 实现视频预览功能
      // 创建一个临时的dialog来预览视频
      this.$alert(
        `<video controls style="width: 100%; max-height: 400px;" src="${src}"></video>`,
        '视频预览',
        {
          dangerouslyUseHTMLString: true,
          showConfirmButton: false,
          callback: () => {}
        }
      );
    },

    deleteVideo(index) {
      // 实现删除视频功能
      if (this.videolist) {
        // 从路径中提取视频路径
        const videoPathParts = this.videolist.split(',');
        
        // 显示确认对话框
        this.$modalSure('是否确认删除该视频？').then(() => {
          try {
            // 直接从数组中移除该视频
            const newVideoPaths = videoPathParts.filter((_, i) => i !== index);
            const newVideoStr = newVideoPaths.join(',');
            
            // 更新数据
            this.videolist = newVideoStr;
            this.$message.success('删除视频成功');
          } catch (err) {
            console.error('删除视频出错:', err);
            this.$message.error('删除视频出错：' + (err.message || '未知错误'));
          }
        }).catch(() => {
          // 用户取消删除操作
          this.$message.info('已取消删除');
        });
      }
    },
  },
};
</script>

<style scoped>
.item-model {
  width: 100%;
}
.demo-tabs > .el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}
</style>
