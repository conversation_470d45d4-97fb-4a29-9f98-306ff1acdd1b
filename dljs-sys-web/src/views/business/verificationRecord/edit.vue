<template>
  <div>
    <el-form ref="pram" :model="pram" :rules="rules" label-width="100px" @submit.native.prevent>
      <el-form-item label="id：" prop="id" v-show="false">
        <el-input v-model.trim="pram.id" placeholder="id"/>
      </el-form-item>
      <el-form-item label="手机号码：" prop="verifyUserPhone">
        <el-input v-model.trim="pram.verifyUserPhone" :disabled="isCreate !== 0" placeholder="请输入手机号码">
          <el-button slot="append" icon="el-icon-paperclip" @click="verifyPhone('pram')"></el-button>
        </el-input>
      </el-form-item>

      <el-alert v-if="type === '1' && isCreate === 0" :title="'当前账号上,可用的龙宫币数量为：' + count" type="success" show-icon :closable="false"></el-alert>
      <el-alert v-if="type === '0' && isCreate === 0" title="请先输入手机号码，获取龙宫币信息" type="info" show-icon :closable="false"></el-alert>
      <el-alert v-if="type === '2' && isCreate === 0" title="当前账号上,可用的龙宫币数量为：0" type="error" show-icon :closable="false"></el-alert>
      <el-form-item></el-form-item>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="核销编码：" prop="verifyCode" v-if="type === '1'">
            <el-input v-model.trim="pram.verifyCode" disabled class="item-model" placeholder="请输入核销编码"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="昵称：" prop="verifyUserName" v-if="type === '1'">
            <el-input v-model.trim="pram.verifyUserName" disabled class="item-model" placeholder="请输入昵称"/>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="核销数量：" prop="verifyNumber" v-if="type === '1'">
        <el-input-number v-model.trim="pram.verifyNumber" :disabled="isCreate !== 0" :min="0" placeholder="请输入核销数量"/>
      </el-form-item>

      <el-form-item label="核销商家：" prop="verifyActionMerchant" v-if="type === '1'">
        <el-input v-model.trim="pram.verifyActionMerchant" :disabled="isCreate !== 0" placeholder="请输入核销商家"/>
      </el-form-item>

      <el-form-item label="核销商品：" prop="verifyProductName" v-if="type === '1'">
        <el-input v-model.trim="pram.verifyProductName" :disabled="isCreate !== 0" placeholder="请输入核销商品"/>
      </el-form-item>

      <el-form-item label="核销商品编号：" prop="verifyProductCode" v-if="type === '1'">
        <el-input v-model.trim="pram.verifyProductCode" :disabled="isCreate !== 0" placeholder="请输入核销商品编号"/>
      </el-form-item>
      <el-form-item></el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer-inner">
      <el-button @click="close">取消</el-button>
      <el-button
        type="primary"
        @click="handlerSubmit('pram')"
        v-hasPermi="['platform:admin:save', 'platform:admin:update']"
      >
        {{ isCreate === 0 ? '确定' : '撤销' }}
      </el-button
      >
    </div>
  </div>
</template>

<script>
// +---------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +---------------------------------------------------------------------
// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
// +---------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +---------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +---------------------------------------------------------------------
import { Debounce } from '@/utils/validate';
import Tinymce from "@/components/Tinymce/index.vue";
import { verifyRecord as api} from '@/utils/api-config';
import { dataInfo } from '@/api/business/receiveRecord';
import * as dataApi from '@/api/business/common';
import { getImgUrl } from "@/utils/imgUtil";
import verify from "@/views/login/verifition/Verify.vue";

export default {
  components: { Tinymce },
  props: {
    isCreate: {
      type: Number,
      required: true,
    },
    editData: {
      type: Object,
      default: () => {
        return {rules: []};
      },
    },
    dictData: {
      type: Object,
      required: true,
      default: () => {
        return {};
      }
    }
  },
  computed: {
    verify() {
      return verify
    },
    dataConfigList() {
      return this.dictData?.dataConfig?.itemList || []
    },
  },
  data() {
    return {
      constants: this.$constants,
      pram: {
        id: null,
        verifyUserPhone: null,
        verifyCode: null,
        verifyUserName: null,
        verifyUserId: null,
        verifyNumber: null,
        verifyActionMerchant: null,
        verifyProductName: null,
        verifyProductCode: null,
        verifyHotspotName: '-',
      },
      roleList: [],
      rules: {
        verifyUserPhone: [{required: true, message: '请输入手机号码', trigger: ['blur', 'change']}],
        verifyCode: [{required: true, message: '请输入核销编码', trigger: ['blur', 'change']}],
        verifyNumber: [{required: true, message: '请输入核销数量', trigger: ['blur', 'change']}],
        verifyActionMerchant: [{required: true, message: '请输入核销商家', trigger: ['blur', 'change']}],
        verifyProductName: [{required: true, message: '请输入核销商品', trigger: ['blur', 'change']}],
        verifyProductCode: [{required: true, message: '请输入核销商品编号', trigger: ['blur', 'change']}],
      },
      keyIndex: '0',
      type: '0',
      count: '0',
    };
  },
  mounted() {
    this.initEditData();
  },
  methods: {
    getImgUrl,
    close() {
      this.$emit('hideEditDialog');
    },
    initEditData() {
      if (this.isCreate !== 1) return;
      this.type = '1';
      this.pram = {...this.editData};
    },
    // 提交数据
    handlerSubmit: Debounce(function (form) {
      this.$refs[form].validate((valid) => {
        try {
          if (!valid) throw new Error('请填写完整信息')
          if (this.isCreate === 0) {
            if (this.type !== '1') throw Error('没有可以核销的数据');
            if (Number(this.count) < this.pram.verifyNumber) throw Error('核销数量不能大于可核销数量');
            this.handlerSave();
          } else {
            this.handlerEdit();
          }
        } catch (e) {
          this.$message({
            message: e,
            type: 'warning',
          });
        }
      });
    }),
    async handlerSave() {
      await dataApi.save(api.path, this.pram);
      this.$message.success(`创建${api.name}成功`);
      this.$emit('hideEditDialog');
    },
    async handlerEdit() {
      await dataApi.update(api.path, this.pram);
      this.$message.success(`更新${api.name}数据成功`);
      this.$emit('hideEditDialog');
    },
    // 点击资讯图
    modalPicTap(multiple) {
      const _this = this;
      const attr = [];
      this.$modalUpload(
        function (img) {
          if (!img) return;
          _this.pram.informCover = img[0].sattDir;
        },
        multiple,
        'store',
      );
    },

    // 验证手机号码上，是否存在可以消费的龙宫币
    verifyPhone: Debounce(function (form) {
      this.$refs[form].validate((valid) => {
        try {
          if (!valid) throw new Error('请填写完整信息')
          this.getReceiveRecord();
        } catch (e) {
          this.$message({
            message: e,
            type: 'warning',
          });
        }
      })
    }),

    // 请求龙宫币数量
    async getReceiveRecord() {
      const res = await dataInfo(this.pram.verifyUserPhone);
      if (res && res.phone === this.pram.verifyUserPhone) {
        this.pram.verifyCode = res.uuid;
        this.pram.verifyUserName = res.name;
        this.pram.verifyUserId = res.id;
        this.count = res.count;
        if (Number(res.count) > 0) {
          this.type = '1';
        } else {
          this.type = '2';
        }
      }
    }
  },
};
</script>

<style scoped>
.item-model {
  width: 100%;
}
</style>
