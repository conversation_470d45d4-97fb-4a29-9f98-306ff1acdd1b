<template>
  <div>
    <el-form ref="pram" :model="pram" :rules="rules" label-width="100px" @submit.native.prevent>
      <el-form-item label="id：" prop="id" v-show="false">
        <el-input v-model.trim="pram.id" placeholder="id"/>
      </el-form-item>
      <el-form-item label="资讯名称：" prop="informTitle">
        <el-input v-model.trim="pram.informTitle" placeholder="请输入资讯名称"/>
      </el-form-item>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="资讯作者：" prop="informAuthor">
            <el-input v-model.trim="pram.informAuthor" class="item-model" placeholder="请输入资讯作者"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="资讯排序：" prop="informTop">
            <el-input-number v-model.trim="pram.informTop" class="item-model" placeholder="请输入资讯排序"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="资讯发布时间：" prop="informPushTime">
            <el-date-picker
              v-model.trim="pram.informPushTime"
              class="item-model"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择资讯时间"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否首页显示：" prop="informIndexShow">
            <el-radio-group v-model.trim="pram.informIndexShow" class="item-model">
              <el-radio v-for="item in dictYnList" :label="item.value">{{item.label}}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="资讯封面图：" prop="informCover">
            <div class="upLoadPicBox" @click="modalPicTap(false)">
              <div v-if="pram.informCover" class="pictrue">
                <img :src="getImgUrl(pram.informCover)[0]" alt=""/>
              </div>
              <div v-else class="upLoad">
                <i class="el-icon-camera cameraIconfont"/>
              </div>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="资讯来源：" prop="informDataSource">
            <el-input v-model.trim="pram.informDataSource" class="item-model" placeholder="请输入资讯来源"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="资讯描述：" prop="informDesc">
        <el-input type="textarea" v-model.trim="pram.informDesc" placeholder="请输入资讯描述"/>
      </el-form-item>
      <el-form-item label="资讯内容：" prop="informContent">
        <Tinymce height="200px" v-model="pram.informContent" :key="keyIndex"></Tinymce>
      </el-form-item>
      <el-form-item></el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer-inner">
      <el-button @click="close">取消</el-button>
      <el-button
        type="primary"
        @click="handlerSubmit('pram')"
        v-hasPermi="['platform:admin:save', 'platform:admin:update']"
      >
        {{ isCreate === 0 ? '确定' : '更新' }}
      </el-button
      >
    </div>
  </div>
</template>

<script>
// +---------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +---------------------------------------------------------------------
// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
// +---------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +---------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +---------------------------------------------------------------------
import { Debounce } from '@/utils/validate';
import Tinymce from "@/components/Tinymce/index.vue";
import { information as api } from '@/utils/api-config';
import * as dataApi from '@/api/business/common';
import { getImgUrl } from "@/utils/imgUtil";

export default {
  components: { Tinymce },
  props: {
    isCreate: {
      type: Number,
      required: true,
    },
    editData: {
      type: Object,
      default: () => {
        return {rules: []};
      },
    },
    dictData: {
      type: Object,
      required: true,
      default: () => {
        return {};
      }
    }
  },
  computed: {
    dictYnList() {
      return this.dictData?.dictYn?.itemList || []
    },
  },
  data() {
    return {
      constants: this.$constants,
      pram: {
        id: null,
        informCover: null,
        informTitle: null,
        informAuthor: null,
        informTop: 0,
        informPushTime: new Date(),
        informIndexShow: "0",
        informDesc: null,
        informContent: null,
        informDataSource: null,
      },
      roleList: [],
      rules: {
        informCover: [{required: true, message: '请上传资讯封面图', trigger: ['blur', 'change']}],
        informTitle: [{required: true, message: '请输入资讯名称', trigger: ['blur', 'change']}],
        // informAuthor: [{required: true, message: '请输入资讯作者', trigger: ['blur', 'change']}],
        informTop: [{required: true, message: '请输入资讯排序', trigger: ['blur', 'change']}],
        informPushTime: [{required: true, message: '请选择资讯发布时间', trigger: ['blur', 'change']}],
        informIndexShow: [{required: true, message: '请选择是否首页显示', trigger: ['blur', 'change']}],
        informContent: [{required: true, message: '请输入资讯内容', trigger: ['blur', 'change']}],
      },
      keyIndex: '0',
    };
  },
  mounted() {
    this.initEditData();
  },
  methods: {
    getImgUrl,
    close() {
      this.$emit('hideEditDialog');
    },
    initEditData() {
      if (this.isCreate !== 1) return;
      this.pram = {...this.editData}
    },
    // 提交数据
    handlerSubmit: Debounce(function (form) {
      this.$refs[form].validate((valid) => {
        try {
          if (!valid) throw new Error('请填写完整信息')
          if (this.isCreate === 0) {
            this.handlerSave();
          } else {
            this.handlerEdit();
          }
        } catch (e) {
          this.$message({
            message: e,
            type: 'warning',
          });
        }
      });
    }),
    async handlerSave() {
      await dataApi.save(api.path, this.pram);
      this.$message.success(`创建${api.name}成功`);
      this.$emit('hideEditDialog');
    },
    async handlerEdit() {
      await dataApi.update(api.path, this.pram);
      this.$message.success(`更新${api.name}数据成功`);
      this.$emit('hideEditDialog');
    },
    // 点击资讯图
    modalPicTap(multiple) {
      const _this = this;
      const attr = [];
      this.$modalUpload(
        function (img) {
          if (!img) return;
          _this.pram.informCover = img[0].sattDir;
        },
        multiple,
        'store',
      );
    },
  },
};
</script>

<style scoped>
.item-model {
  width: 100%;
}
</style>
