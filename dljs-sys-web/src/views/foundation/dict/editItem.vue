<template>
    <div>
      <el-form ref="pram" :model="pram" label-width="75px" @submit.native.prevent>
        <el-form-item
          label="名称:"
          prop="itemName"
          :rules="[{ required: true, message: '请输入名称', trigger: ['blur', 'change'] }]"
        >
          <el-input v-model.trim="pram.itemName" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item
          label="编码:"
          prop="itemCode"
          :rules="[{ required: true, message: '请输入编码', trigger: ['blur', 'change'] }]"
        >
          <el-input v-model.trim="pram.itemCode" placeholder="请输入编码" />
        </el-form-item>
        <el-form-item
          label="描述:"
          prop="itemDesc"
          :rules="[{ required: false, message: '请输入描述'}]"
        >
          <el-input v-model.trim="pram.itemDesc" placeholder="请输入描述" />
        </el-form-item>
        <el-form-item
          label="排序:"
          prop="itemSort"
          :rules="[{ required: false}]"
        >
          <el-input-number v-model.trim="pram.itemSort" />
        </el-form-item>
        <el-form-item
            label="是否启用:"
            prop="itemEnable">
        <el-radio-group v-model="pram.itemEnable">
            <el-radio-button label="启用"></el-radio-button>
            <el-radio-button label="不启用"></el-radio-button>
        </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer-inner">
        <el-button @click="close">取消</el-button>
        <el-button
          type="primary"
          @click="handlerSubmit('pram')"
     
        >
          {{ isCreate === 0 ? '确定' : '更新' }}</el-button
        >
      </div>
    </div>
  </template>
  
  <script>
  
  import * as dictApi from '@/api/dict.js';
  import { Debounce } from '@/utils/validate';
  export default {
    name: 'dictEdit',
    props: {
      isCreate: {
        type: Number,
        required: true,
      },
      editData: {
        type: Object,
        default: null,
      },
    },
    data() {
      return { 
        pram: {
          itemEnable: '启用',
          itemName: null,
          itemCode: null,
          itemSort: null,
          itemDesc: null,
          id: null,
        },
        defaultProps: {
          children: 'childList',
          label: 'name',
        },
      };
    },
    mounted() {
      this.initEditData();
    },
    methods: {
      close() {
        this.$emit('hideEditDialog');
      },
      initEditData() {
        this.pram.dictId = this.editData.dictId;
        if (this.isCreate !== 1) return;
        const { itemName,itemCode,dictId,itemSort,itemDesc, id } = this.editData;
        this.pram.itemName = itemName;
        this.pram.itemCode = itemCode;
        this.pram.itemSort = itemSort;
        this.pram.itemDesc = itemDesc;
        this.pram.dictId = dictId;
        this.pram.id = id;
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
        });
        dictApi.getDictItemInfo(id).then((res) => {
          loading.close();
        });
      },
      handlerSubmit: Debounce(function (form) {
        this.$refs[form].validate((valid) => {
          if (!valid) return;
          if (this.isCreate === 0) {
            this.handlerSave();
          } else {
            this.handlerEdit();
          }
        });
      }),
      handlerSave() {
        dictApi.addDictItem(this.pram).then((data) => {
          this.$message.success('保存成功');
          this.$emit('hideEditDialog');
        });
      },
      handlerEdit() {
        dictApi.updateDictItem(this.pram).then((data) => {
          this.$message.success('操作成功');
          this.$emit('hideEditDialog');
        });
      },
    },
  };
  </script>
  
  <style scoped></style>
  