<template>
  <div class="divBox">
    <el-card class="box-card" :bordered="false" shadow="never" :body-style="{ padding: 0 }">
      <div class="padding-add">
        <el-form inline size="small" @submit.native.prevent>
          <el-form-item label="字典名称">
            <el-input
              v-model.trim="listPram.dictName"
              @keyup.enter.native="handleGetDictList"
              placeholder="请输入字典名称"
              clearable
              class="selWidth"
            />
          </el-form-item>
          <el-form-item label="字典编码">
            <el-input
              v-model.trim="listPram.dictCode"
              @keyup.enter.native="handleGetDictList"
              placeholder="请输入字典编码"
              clearable
              class="selWidth"
            />
          </el-form-item>
          <el-form-item>
            <el-button size="mini" type="primary" @click.native="handleGetDictList">查询</el-button>
            <el-button size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <el-card class="box-card mt14" :body-style="{ padding: '20px' }" shadow="never" :bordered="false">
      <el-form inline @submit.native.prevent>
        <el-form-item>
          <el-button size="mini" type="primary" @click="handlerOpenEdit(0)" v-hasPermi="['platform:admin:role:save']"
            >新增</el-button
          >
        </el-form-item>
      </el-form>
      <el-table
        :data="listData.list"
        size="small"
        :header-cell-style="{ fontWeight: 'bold', background: '#f8f8f9', color: '#515a6e', height: '40px' }"
      >
        <el-table-column label="字典名称" prop="dictName" width="150"></el-table-column>
        <el-table-column label="字典编码" prop="dictCode" min-width="150" />
        <el-table-column label="描述" prop="dictDesc" min-width="150" />
        <el-table-column label="操作" width="150" fixed="right">
          <template slot-scope="scope">
            <a @click="handlerOpenEdit(1, scope.row)">编辑</a>
            <el-divider direction="vertical"></el-divider>
            <a @click="onDetails(scope.row.id)">字典配置</a>
            <el-divider direction="vertical"></el-divider>
            <a @click="handlerOpenDel(scope.row)">删除</a>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page="listPram.page"
        :page-sizes="constants.page.limit"
        :layout="constants.page.layout"
        :total="listData.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
    <el-dialog
      :visible.sync="editDialogConfig.visible"
      :title="editDialogConfig.isCreate === 0 ? '新增字典' : '编辑字典'"
      destroy-on-close
      :close-on-click-modal="false"
      width="500px"
      class="dialog-bottom"
    >
      <edit
        v-if="editDialogConfig.visible"
        :is-create="editDialogConfig.isCreate"
        :edit-data="editDialogConfig.editData"
        @hideEditDialog="hideEditDialog"
        ref="editForm"
      />
    </el-dialog>
    <!--字典配置-->
    <!-- <el-drawer
      title="字典列表"
      :visible.sync="dictItemConfig.visible"
      size="800px"
    > -->
    <dictConfig ref="userDetailFrom" />
  <!-- </el-drawer> -->
  </div>
</template>

<script>
import * as dictApi from '@/api/dict.js';
import edit from './edit';
import dictConfig from './dictConfig';
import { checkPermi } from '@/utils/permission'; // 权限判断函数
export default {
  // name: "index"
  components: { edit, dictConfig },
  data() {
    return {
      constants: this.$constants,
      listData: { list: [] },
      listPram: {
        page: 1,
        limit: this.$constants.page.limit[0],
        dictName: null,
        dictCode: null,
      },
      menuList: [],
      editDialogConfig: {
        visible: false,
        isCreate: 0, // 0=创建，1=编辑
        editData: {},
      },
      dictItemConfig: {
        visible: false,
        editData: {},
      },
    };
  },
  mounted() {
    this.handleGetDictList();
  },
  methods: {
    handlerOpenDel(rowData) {
      this.$modalSure('确认删除当前数据').then(() => {
        dictApi.delDict(rowData.id).then((data) => {
          this.$message.success('删除数据成功');
          this.handleGetDictList();
        });
      });
    },
    handleGetDictList() {
      dictApi.getDictList(this.listPram).then((data) => {
        this.listData = data;
      });
    },
    handlerOpenEdit(isCreate, editDate) {
      isCreate === 1 ? (this.editDialogConfig.editData = editDate) : (this.editDialogConfig.editData = {});
      this.editDialogConfig.isCreate = isCreate;
      this.editDialogConfig.visible = true;
    },
    hideEditDialog() {
      this.editDialogConfig.visible = false;
      this.handleGetDictList();
    },
    handleSizeChange(val) {
      this.listPram.limit = val;
      this.handleGetDictList(this.listPram);
    },
    handleCurrentChange(val) {
      this.listPram.page = val;
      this.handleGetDictList(this.listPram);
    },

    onDetails(id) {
      // this.dictItemConfig.editData = data;
      // this.dictItemConfig.visible = true;
      this.$refs.userDetailFrom.initData(id);
      this.$refs.userDetailFrom.dialogUserDetail = true;
    },

    resetQuery() {
      this.listPram = {
        createTime: null,
        updateTime: null,
        level: null,
        page: 1,
        limit: this.$constants.page.limit[0],
      };
      this.handleGetDictList();
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep .el-drawer__header {
  display: flex !important;
  align-items: flex-start !important;
  margin: 0 !important;
  padding: 15px 15px 0 15px !important;
}
::v-deep .demo-drawer_title {
  width: 90%;
}
::v-deep .el-drawer__body {
  padding: 0 0 30px 0 !important;
}
</style>
