<template>
  <div>
    <el-drawer
      title="字典列表"
      :visible.sync="dialogUserDetail"
      size="800px"
    >
      <div class="padding-add">
        <el-form inline size="small" @submit.native.prevent>
          <el-form-item label="名称">
            <el-input
              v-model.trim="itemListPram.itemName"
              @keyup.enter.native="handleGetDictItemList"
              placeholder="请输入字典名称"
              clearable
              class="selWidth"
            />
          </el-form-item>
          <el-form-item>
            <el-button size="mini" type="primary" @click.native="handleGetDictItemList">查询</el-button>
            <el-button size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div style="padding: 0px 20px 0;" :bordered="false">
       <el-form inline @submit.native.prevent>
         <el-form-item>
           <el-button size="mini" type="primary" @click="handlerOpenEdit(0)"
             >新增</el-button
           >
         </el-form-item>
       </el-form>
       <el-table
         :data="listData.list"
         size="small"
         :header-cell-style="{ fontWeight: 'bold', background: '#f8f8f9', color: '#515a6e', height: '40px' }"
       >
         <el-table-column label="名称" prop="itemName" width="150"></el-table-column>
         <el-table-column label="编码" prop="itemCode" min-width="150" />
         <el-table-column label="描述" prop="itemDesc" min-width="150" />
         <el-table-column label="操作" width="150" fixed="right">
           <template slot-scope="scope">
             <a @click="handlerOpenEdit(1, scope.row)">编辑</a>
             <el-divider direction="vertical"></el-divider>
             <a @click="handlerOpenDel(scope.row)">删除</a>
           </template>
         </el-table-column>
       </el-table>
       <el-pagination
         background
         :current-page="itemListPram.page"
         :page-sizes="constants.page.limit"
         :layout="constants.page.layout"
         :total="listData.total"
         @size-change="handleSizeChange"
         @current-change="handleCurrentChange"
       />
    </div>
</el-drawer>
    <el-dialog
       :visible.sync="editDialogConfig.visible"
       :title="editDialogConfig.isCreate === 0 ? '新增' : '编辑'"
       destroy-on-close
       :close-on-click-modal="false"
       width="500px"
       class="dialog-bottom"
     >
       <editItem
         v-if="editDialogConfig.visible"
         :is-create="editDialogConfig.isCreate"
         :edit-data="editDialogConfig.editData"
         @hideEditDialog="hideEditDialog"
         ref="editItemForm"
       />
     </el-dialog>

  </div>
  
</template>

<script>
import * as dictApi from '@/api/dict.js';
import editItem from './editItem';
import { checkPermi } from '@/utils/permission'; // 权限判断函数
export default {
  // name: "index"
//   props: {
//     editData: {
//       type: Object,
//       default: null,
//     },
//   },
  components: { editItem },
  data() {
    return {
      constants: this.$constants,
      dialogUserDetail: false,
      listData: { list: [] },
      itemListPram: {
        page: 1,
        limit: this.$constants.page.limit[0],
        itemName: null,
        dictId: null,
        itemEnable: null,
      },
      editDialogConfig: {
        visible: false,
        isCreate: 0, // 0=创建，1=编辑
        editData: {},
      },
    };
  },
  mounted() {
    // this.initData();
  },
  methods: {
    initData(id) {
      this.itemListPram.dictId = id;
      this.itemListPram.itemEnable = '启用';
      this.handleGetDictItemList();
    },

    handlerOpenDel(rowData) {
      this.$modalSure('确认删除当前数据').then(() => {
        dictApi.delDictItem(rowData.id).then((data) => {
          this.$message.success('删除数据成功');
          this.handleGetDictItemList();
        });
      });
    },
    handleGetDictItemList() {
      dictApi.getDictItemList(this.itemListPram).then((data) => {
        this.listData = data;
      });
    },
    handlerOpenEdit(isCreate, editDate) {
      isCreate === 1? (this.editDialogConfig.editData = editDate): (this.editDialogConfig.editData = { dictId: this.itemListPram.dictId });
      this.editDialogConfig.isCreate = isCreate;
      this.editDialogConfig.visible = true;
    },
    hideEditDialog() {
      this.editDialogConfig.visible = false;
      this.handleGetDictItemList();
    },
    handleSizeChange(val) {
      this.itemListPram.limit = val;
      this.handleGetDictItemList(this.itemListPram);
    },
    handleCurrentChange(val) {
      this.itemListPram.page = val;
      this.handleGetDictItemList(this.itemListPram);
    },

    resetQuery() {
      this.itemListPram = {
        createTime: null,
        updateTime: null,
        page: 1,
        limit: this.$constants.page.limit[0],
      };
      this.handleGetDictItemList();
    },
  },
};
</script>

<style scoped lang="scss">

</style>
