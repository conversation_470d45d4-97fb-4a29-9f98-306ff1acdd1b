@import './mixin.scss';
@import './styles.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: ' ';
    clear: both;
    height: 0;
  }
}
//main-container全局样式
.app-container {
  padding: 20px;
}

.components-container {
  position: relative;
}

.pagination-container {
  margin-top: 30px;
}

.text-center {
  text-align: center;
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

.link {
  color: #409eff;
  cursor: pointer;
}
.color-red {
  color: #f56c6c;
}
.btn-width100 {
  width: 100%;
}

.f-w-500 {
  font-weight: 500;
}
.f-s-18 {
  font-size: 18px;
}

.row_title {
  width: 170px !important;
}
.desc {
  color: #999;
  font-size: 12px;
  line-height: 16px;
  margin: 0;
}

.font14 {
  font-size: 14px;
}
.line-height-15 {
  line-height: 1.5;
}
.line-heightOne {
  line-height: 1;
}
.textE93323 {
  color: #e93323 !important;
}
.bg-color {
  background-color: #e93323;
}
.color-FAAD14 {
  background-color: #faad14;
}
.text333 {
  color: #333 !important;
}
.text999 {
  color: #999;
}
.text666 {
  color: #666;
}
.font12 {
  font-size: 12px;
}
