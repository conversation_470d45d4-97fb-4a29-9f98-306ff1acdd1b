// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

import request from '@/utils/request';

// ==================== 房型管理 ====================

/**
 * 分页查询房型列表
 */
export function hotelRoomListApi(params) {
  return request({
    url: '/admin/hotel/room/list',
    method: 'GET',
    params,
  });
}

/**
 * 新增房型
 */
export function hotelRoomCreateApi(data) {
  return request({
    url: '/admin/hotel/room/save',
    method: 'POST',
    data,
  });
}

/**
 * 新增房型和价格策略
 */
export function hotelRoomCreateWithPricesApi(data) {
  return request({
    url: '/admin/hotel/room/saveWithPrices',
    method: 'POST',
    data,
  });
}

/**
 * 修改房型
 */
export function hotelRoomUpdateApi(data) {
  return request({
    url: '/admin/hotel/room/update',
    method: 'POST',
    data,
  });
}

/**
 * 获取房型详情
 */
export function hotelRoomDetailApi(id) {
  return request({
    url: `/admin/hotel/room/info/${id}`,
    method: 'GET',
  });
}

/**
 * 删除房型
 */
export function hotelRoomDeleteApi(id) {
  return request({
    url: `/admin/hotel/room/delete/${id}`,
    method: 'POST',
  });
}

/**
 * 修改房型状态
 */
export function hotelRoomStatusApi(id, status) {
  return request({
    url: `/admin/hotel/room/status/${id}/${status}`,
    method: 'POST',
  });
}

/**
 * 修改房型库存
 */
export function hotelRoomStockApi(id, totalRooms) {
  return request({
    url: `/admin/hotel/room/stock/${id}/${totalRooms}`,
    method: 'POST',
  });
}

// ==================== 价格策略管理 ====================

/**
 * 分页查询价格策略列表
 */
export function hotelPriceListApi(params) {
  return request({
    url: '/admin/hotel/price/list',
    method: 'GET',
    params,
  });
}

/**
 * 新增价格策略
 */
export function hotelPriceCreateApi(data) {
  return request({
    url: '/admin/hotel/price/save',
    method: 'POST',
    data,
  });
}

/**
 * 修改价格策略
 */
export function hotelPriceUpdateApi(data) {
  return request({
    url: '/admin/hotel/price/update',
    method: 'POST',
    data,
  });
}

/**
 * 获取价格策略详情
 */
export function hotelPriceDetailApi(id) {
  return request({
    url: `/admin/hotel/price/info/${id}`,
    method: 'GET',
  });
}

/**
 * 删除价格策略
 */
export function hotelPriceDeleteApi(id) {
  return request({
    url: `/admin/hotel/price/delete/${id}`,
    method: 'POST',
  });
}

/**
 * 修改价格策略状态
 */
export function hotelPriceStatusApi(id, status) {
  return request({
    url: `/admin/hotel/price/status/${id}/${status}`,
    method: 'POST',
  });
}

/**
 * 获取价格日历
 */
export function hotelPriceCalendarApi(params) {
  return request({
    url: '/admin/hotel/price/calendar',
    method: 'GET',
    params,
  });
}

// ==================== 取消规则管理 ====================

/**
 * 分页查询取消规则列表
 */
export function hotelCancelListApi(params) {
  return request({
    url: '/admin/hotel/cancel/list',
    method: 'GET',
    params,
  });
}

/**
 * 新增取消规则
 */
export function hotelCancelCreateApi(data) {
  return request({
    url: '/admin/hotel/cancel/save',
    method: 'POST',
    data,
  });
}

/**
 * 修改取消规则
 */
export function hotelCancelUpdateApi(data) {
  return request({
    url: '/admin/hotel/cancel/update',
    method: 'POST',
    data,
  });
}

/**
 * 获取取消规则详情
 */
export function hotelCancelDetailApi(id) {
  return request({
    url: `/admin/hotel/cancel/info/${id}`,
    method: 'GET',
  });
}

/**
 * 删除取消规则
 */
export function hotelCancelDeleteApi(id) {
  return request({
    url: `/admin/hotel/cancel/delete/${id}`,
    method: 'POST',
  });
}

/**
 * 修改取消规则状态
 */
export function hotelCancelStatusApi(id, status) {
  return request({
    url: `/admin/hotel/cancel/status/${id}/${status}`,
    method: 'POST',
  });
}

/**
 * 计算取消费用
 */
export function hotelCancelCalculateApi(params) {
  return request({
    url: '/admin/hotel/cancel/calculate',
    method: 'GET',
    params,
  });
}
