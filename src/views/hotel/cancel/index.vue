<template>
  <div class="divBox relative">
    <!-- 搜索条件 -->
    <el-card
      v-if="$auth.hasPermi('admin:hotel:cancel:list')"
      :bordered="false"
      shadow="never"
      class="ivu-mt"
      :body-style="{ padding: 0 }"
    >
      <div class="padding-add">
        <el-form inline size="small" label-position="right" @submit.native.prevent>
          <el-form-item label="规则名称：">
            <el-input
              v-model.trim="searchForm.ruleName"
              placeholder="请输入规则名称"
              class="form_content_width"
              size="small"
              @keyup.enter.native="handleSearch"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="扣费类型：">
            <el-select
              v-model="searchForm.penaltyType"
              clearable
              size="small"
              placeholder="请选择扣费类型"
              class="form_content_width"
            >
              <el-option label="按比例扣费" :value="1"/>
              <el-option label="固定金额扣费" :value="2"/>
            </el-select>
          </el-form-item>
          <el-form-item label="状态：">
            <el-select
              v-model="searchForm.status"
              clearable
              size="small"
              placeholder="请选择状态"
              class="selWidth"
            >
              <el-option label="启用" :value="1"/>
              <el-option label="禁用" :value="0"/>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" @click="handleSearch">查询</el-button>
            <el-button size="small" @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 操作按钮和表格 -->
    <el-card class="box-card mt14" :body-style="{ padding: '0 20px 20px' }" shadow="never" :bordered="false">
      <div class="clearfix mb20">
        <el-button size="small" type="primary" v-hasPermi="['admin:hotel:cancel:save']" @click="handleAdd">
          添加取消规则
        </el-button>
        <el-button size="small" type="success" @click="handleCalculator" v-hasPermi="['admin:hotel:cancel:calculate']">
          费用计算器
        </el-button>
      </div>

      <!-- 表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column prop="ruleName" label="规则名称" min-width="120"/>
        <el-table-column prop="penaltyTypeDesc" label="扣费类型" min-width="100"/>
        <el-table-column prop="advanceTimeDesc" label="提前取消时间" min-width="140"/>
        <el-table-column prop="penaltyValue" label="扣除金额" width="100" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.penaltyType === 2">¥{{ scope.row.penaltyValue }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="penaltyValue" label="扣除百分比" width="100" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.penaltyType === 1">{{ scope.row.penaltyValue }}%</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="规则描述" min-width="180" show-overflow-tooltip/>
        <!--        <el-table-column prop="sort" label="排序" width="80" align="center" />-->
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(scope.row)"
              v-hasPermi="['admin:hotel:cancel:status']"
            />
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160" align="center">
          <template slot-scope="scope">
            {{ parseTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="190" align="center" fixed="right">
          <template slot-scope="scope">
            <a @click="handleView(scope.row)" v-hasPermi="['admin:hotel:cancel:info']">查看</a>
            <el-divider direction="vertical"></el-divider>
            <a @click="handleEdit(scope.row)" v-hasPermi="['admin:hotel:cancel:update']">编辑</a>
            <el-divider direction="vertical"></el-divider>
            <a @click="handleDelete(scope.row)" style="color: #f56c6c"
               v-hasPermi="['admin:hotel:cancel:delete']">删除</a>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="block">
        <el-pagination
          v-show="total > 0"
          background
          :page-sizes="[10, 20, 30, 40]"
          :page-size="queryParams.limit"
          :current-page="queryParams.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <cancel-form
      v-if="formVisible"
      :visible="formVisible"
      :form-data="formData"
      :is-edit="isEdit"
      @close="handleFormClose"
      @success="handleFormSuccess"
    />

    <!-- 费用计算器对话框 -->
    <cancel-calculator
      v-if="calculatorVisible"
      :visible="calculatorVisible"
      @close="handleCalculatorClose"
    />
  </div>
</template>

<script>
import {hotelCancelDeleteApi, hotelCancelListApi, hotelCancelStatusApi} from '@/api/hotel';
import CancelForm from './components/CancelForm.vue';
import CancelCalculator from './components/CancelCalculator.vue';
import {parseTime} from '@/utils/index';

export default {
  name: 'HotelCancel',
  components: {
    CancelForm,
    CancelCalculator,
  },
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0,
      queryParams: {
        page: 1,
        limit: 10,
      },
      searchForm: {
        ruleName: '',
        penaltyType: null,
        status: null,
      },
      selectedRows: [],
      formVisible: false,
      formData: {},
      isEdit: false,
      calculatorVisible: false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    parseTime,

    /** 查询列表 */
    getList() {
      this.loading = true;
      const params = {
        ...this.queryParams,
        ...this.searchForm,
      };
      hotelCancelListApi(params)
        .then((response) => {
          if (response && response.list) {
            this.tableData = response.list;
            this.total = response.total || 0;
          } else if (response && Array.isArray(response)) {
            this.tableData = response;
            this.total = response.length;
          } else {
            console.warn('取消规则列表数据结构异常:', response);
            this.tableData = [];
            this.total = 0;
          }
        })
        .catch((error) => {
          console.error('获取取消规则列表失败:', error);
          this.tableData = [];
          this.total = 0;
          this.$message.error('获取取消规则列表失败，请稍后重试');
        })
        .finally(() => {
          this.loading = false;
        });
    },

    /** 搜索 */
    handleSearch() {
      this.queryParams.page = 1;
      this.getList();
    },

    /** 重置 */
    handleReset() {
      this.searchForm = {
        ruleName: '',
        penaltyType: null,
        status: null,
      };
      this.handleSearch();
    },

    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },

    /** 新增 */
    handleAdd() {
      this.formData = {};
      this.isEdit = false;
      this.formVisible = true;
    },

    /** 查看 */
    handleView(row) {
      this.formData = {...row};
      this.isEdit = false;
      this.formVisible = true;
    },

    /** 编辑 */
    handleEdit(row) {
      this.formData = {...row};
      this.isEdit = true;
      this.formVisible = true;
    },

    /** 删除 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除取消规则"' + row.ruleName + '"？').then(() => {
        return hotelCancelDeleteApi(row.id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess('删除成功');
      });
    },

    /** 状态修改 */
    handleStatusChange(row) {
      const text = row.status === 1 ? '启用' : '禁用';
      this.$modal.confirm('确认要"' + text + '""' + row.ruleName + '"取消规则吗？').then(() => {
        return hotelCancelStatusApi(row.id, row.status);
      }).then(() => {
        this.$modal.msgSuccess(text + '成功');
      }).catch(() => {
        row.status = row.status === 0 ? 1 : 0;
      });
    },

    /** 费用计算器 */
    handleCalculator() {
      this.calculatorVisible = true;
    },

    /** 表单关闭 */
    handleFormClose() {
      this.formVisible = false;
    },

    /** 表单提交成功 */
    handleFormSuccess() {
      this.formVisible = false;
      this.getList();
    },

    /** 计算器关闭 */
    handleCalculatorClose() {
      this.calculatorVisible = false;
    },

    /** 分页大小改变 */
    handleSizeChange(val) {
      this.queryParams.limit = val;
      this.queryParams.page = 1;
      this.getList();
    },

    /** 当前页改变 */
    handleCurrentChange(val) {
      this.queryParams.page = val;
      this.getList();
    },
  },
};
</script>

<style scoped>
.form_content_width {
  width: 200px;
}

.selWidth {
  width: 120px;
}
</style>
