<template>
  <el-dialog
    :title="isEdit ? '编辑取消规则' : (formData.id ? '查看取消规则' : '新增取消规则')"
    :visible="visible"
    width="600px"
    append-to-body
    @close="handleClose"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
      :disabled="!isEdit && formData.id"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="规则名称" prop="ruleName">
            <el-input v-model="form.ruleName" placeholder="请输入规则名称"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="扣费类型" prop="penaltyType">
            <el-select v-model="form.penaltyType" placeholder="请选择扣费类型" style="width: 100%"
                       @change="handlePenaltyTypeChange">
              <el-option label="按比例扣费" :value="1"/>
              <el-option label="固定金额扣费" :value="2"/>
            </el-select>
            <div style="font-size: 12px; color: #999; margin-top: 4px;">
              💡 免费取消：选择"按比例扣费"，扣费值设为0
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="提前取消小时数" prop="advanceHours">
            <el-input-number
              v-model="form.advanceHours"
              :min="0"
              :max="8760"
              placeholder="请输入提前取消小时数"
              style="width: 100%"
            />
            <div style="font-size: 12px; color: #999; margin-top: 4px;">0表示随时可取消</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sort">
            <el-input-number
              v-model="form.sort"
              :min="0"
              :max="999"
              placeholder="请输入排序"
              style="width: 100%"
            />
            <div style="font-size: 12px; color: #999; margin-top: 4px;">数值越大排序越靠前</div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 扣费值 -->
      <el-row :gutter="20" v-if="form.penaltyType">
        <el-col :span="12">
          <el-form-item label="扣费值" prop="penaltyValue">
            <el-input-number
              v-model="form.penaltyValue"
              :min="0"
              :max="form.penaltyType === 1 ? 100 : 99999.99"
              :precision="2"
              :placeholder="form.penaltyType === 1 ? '请输入扣费比例(0-100)' : '请输入扣费金额'"
              style="width: 100%"
            />
            <div style="font-size: 12px; color: #999; margin-top: 4px;">
              {{
                form.penaltyType === 1 ? '按比例扣费：0-100之间的数值（0=免费取消）' : '固定金额扣费：具体扣费金额（0=免费取消）'
              }}
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="规则描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="4"
          placeholder="请输入规则描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <!-- 规则说明 -->
      <el-form-item label="规则说明">
        <div class="rule-explanation">
          <div class="rule-item">
            <strong>免费取消：</strong>选择"按比例扣费"，扣费值设为0，实现免费取消
          </div>
          <div class="rule-item">
            <strong>按比例扣费：</strong>在指定时间前取消订单，按订单金额的百分比扣除取消费用
          </div>
          <div class="rule-item">
            <strong>固定金额扣费：</strong>在指定时间前取消订单，扣除固定金额作为取消费用
          </div>
          <div class="rule-item">
            <strong>排序规则：</strong>数值越大排序越靠前，系统按提前时间从大到小匹配规则
          </div>
        </div>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer" v-if="isEdit || !formData.id">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {hotelCancelCreateApi, hotelCancelUpdateApi} from '@/api/hotel';

export default {
  name: 'CancelForm',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: () => ({}),
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      form: {
        id: null,
        ruleName: '',
        penaltyType: null,
        penaltyValue: null,
        advanceHours: 0,
        sort: 0,
        status: 1,
        description: '',
      },
      submitLoading: false,
      rules: {
        ruleName: [
          {required: true, message: '规则名称不能为空', trigger: 'blur'},
          {max: 100, message: '规则名称长度不能超过100个字符', trigger: 'blur'},
        ],
        penaltyType: [
          {required: true, message: '扣费类型不能为空', trigger: 'change'},
        ],
        penaltyValue: [
          {required: true, message: '扣费值不能为空', trigger: 'blur'},
        ],
        advanceHours: [
          {required: true, message: '提前取消小时数不能为空', trigger: 'blur'},
        ],
        sort: [
          {required: true, message: '排序不能为空', trigger: 'blur'},
        ],
        status: [
          {required: true, message: '状态不能为空', trigger: 'change'},
        ],
      },
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm();
      }
    },
    formData: {
      handler(val) {
        if (val && this.visible) {
          this.initForm();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    /** 初始化表单 */
    initForm() {
      if (this.formData.id) {
        this.form = {...this.formData};
      } else {
        this.form = {
          id: null,
          ruleName: '',
          penaltyType: null,
          penaltyValue: null,
          advanceHours: 0,
          sort: 0,
          status: 1,
          description: '',
        };
      }
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
        }
      });
    },

    /** 扣费类型变化 */
    handlePenaltyTypeChange(value) {
      // 清空扣费值
      this.form.penaltyValue = null;

      // 清除验证
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate(['penaltyValue']);
        }
      });
    },

    /** 提交 */
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 验证扣费类型对应的参数
          if (this.form.penaltyType === 1 && (this.form.penaltyValue < 0 || this.form.penaltyValue > 100)) {
            this.$modal.msgError('按比例扣费时，扣费值必须在0-100之间');
            return;
          }
          if (this.form.penaltyType === 2 && this.form.penaltyValue < 0) {
            this.$modal.msgError('固定金额扣费时，扣费值不能小于0');
            return;
          }

          this.submitLoading = true;
          const api = this.form.id ? hotelCancelUpdateApi : hotelCancelCreateApi;
          api(this.form)
            .then(() => {
              this.$modal.msgSuccess(this.form.id ? '修改成功' : '新增成功');
              this.$emit('success');
            })
            .finally(() => {
              this.submitLoading = false;
            });
        }
      });
    },

    /** 关闭 */
    handleClose() {
      this.$emit('close');
    },
  },
};
</script>

<style scoped>
.rule-explanation {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  font-size: 13px;
  line-height: 1.6;
}

.rule-item {
  margin-bottom: 8px;
  color: #606266;
}

.rule-item:last-child {
  margin-bottom: 0;
}

.rule-item strong {
  color: #303133;
}
</style>
