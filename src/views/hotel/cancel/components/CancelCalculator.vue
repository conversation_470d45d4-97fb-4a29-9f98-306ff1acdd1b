<template>
  <el-dialog
    title="取消费用计算器"
    :visible="visible"
    width="500px"
    append-to-body
    @close="handleClose"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item label="订单金额" prop="orderAmount">
        <el-input-number
          v-model="form.orderAmount"
          :min="0.01"
          :max="99999.99"
          :precision="2"
          placeholder="请输入订单金额"
          style="width: 100%"
        />
        <div style="font-size: 12px; color: #999; margin-top: 4px;">单位：元</div>
      </el-form-item>

      <el-form-item label="提前取消小时数" prop="advanceHours">
        <el-input-number
          v-model="form.advanceHours"
          :min="0"
          :max="8760"
          placeholder="请输入提前取消小时数"
          style="width: 100%"
        />
        <div style="font-size: 12px; color: #999; margin-top: 4px;">距离入住时间的小时数</div>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleCalculate" :loading="calculating">计算取消费用</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 计算结果 -->
    <div v-if="result.calculated" class="result-section">
      <el-divider content-position="left">计算结果</el-divider>

      <div class="result-card">
        <div class="result-item">
          <span class="label">订单金额：</span>
          <span class="value">¥{{ form.orderAmount }}</span>
        </div>
        <div class="result-item">
          <span class="label">提前取消时间：</span>
          <span class="value">{{ getAdvanceTimeDesc(form.advanceHours) }}</span>
        </div>
        <div class="result-item">
          <span class="label">匹配规则：</span>
          <span class="value">{{ result.ruleName || '无匹配规则' }}</span>
        </div>
        <div class="result-item highlight">
          <span class="label">取消费用：</span>
          <span class="value fee" v-if="isNotCancelable">不可取消</span>
          <span class="value fee" v-else>¥{{ result.cancelFee }}</span>
        </div>
        <div class="result-item highlight" v-if="!isNotCancelable">
          <span class="label">退款金额：</span>
          <span class="value refund">¥{{ (form.orderAmount - result.cancelFee).toFixed(2) }}</span>
        </div>
      </div>

      <!-- 费用说明 -->
      <div class="fee-explanation">
        <div class="explanation-title">费用说明：</div>
        <div class="explanation-content">
          <div v-if="isNotCancelable" class="explanation-item error">
            <i class="el-icon-circle-close"></i>
            提前取消时间不足，不可以取消订单
          </div>
          <div v-else-if="result.cancelFee === 0" class="explanation-item success">
            <i class="el-icon-circle-check"></i>
            免费取消，无需支付取消费用
          </div>
          <div v-else class="explanation-item warning">
            <i class="el-icon-warning"></i>
            根据取消规则，需要支付 ¥{{ result.cancelFee }} 的取消费用
          </div>
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {hotelCancelCalculateApi} from '@/api/hotel';

export default {
  name: 'CancelCalculator',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      form: {
        orderAmount: null,
        advanceHours: null,
      },
      calculating: false,
      result: {
        calculated: false,
        cancelFee: 0,
        ruleName: '',
      },
      rules: {
        orderAmount: [
          {required: true, message: '订单金额不能为空', trigger: 'blur'},
        ],
        advanceHours: [
          {required: true, message: '提前取消小时数不能为空', trigger: 'blur'},
        ],
      },
    };
  },
  computed: {
    /** 判断是否不可取消 */
    isNotCancelable() {
      const fee = this.result.cancelFee;
      return fee === -1 || fee === '-1' || Number(fee) === -1;
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm();
      }
    },
  },
  methods: {
    /** 初始化表单 */
    initForm() {
      this.form = {
        orderAmount: null,
        advanceHours: null,
      };
      this.result = {
        calculated: false,
        cancelFee: 0,
        ruleName: '',
      };
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
        }
      });
    },

    /** 计算取消费用 */
    handleCalculate() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.calculating = true;
          hotelCancelCalculateApi({
            orderAmount: this.form.orderAmount,
            advanceHours: this.form.advanceHours,
          })
            .then((response) => {
              let cancelFee = response;
              if (typeof cancelFee === 'string') {
                cancelFee = parseFloat(cancelFee);
              }
              if (cancelFee === null || cancelFee === undefined || isNaN(cancelFee)) {
                cancelFee = 0;
              }
              // 检查是否不可取消(-1表示不可取消)
              if (cancelFee === -1 || cancelFee === '-1' || Number(cancelFee) === -1) {
                this.result = {
                  calculated: true,
                  cancelFee: -1,
                  ruleName: '不可取消',
                  canCancel: false
                };
              } else {
                this.result = {
                  calculated: true,
                  cancelFee: cancelFee,
                  ruleName: this.getRuleNameByFee(cancelFee),
                  canCancel: true
                };
              }
            })
            .catch(() => {
              this.result = {
                calculated: true,
                cancelFee: 0,
                ruleName: '计算失败',
              };
            })
            .finally(() => {
              this.calculating = false;
            });
        }
      });
    },

    /** 重置 */
    handleReset() {
      this.initForm();
    },

    /** 根据费用推断规则名称 */
    getRuleNameByFee(fee) {
      if (fee === 0) {
        return '免费取消规则';
      } else if (fee === -1) {
        return '不可取消规则';
      } else if (fee === this.form.orderAmount) {
        return '全额扣除规则';
      } else {
        return '部分扣除规则';
      }
    },

    /** 获取提前取消时间描述 */
    getAdvanceTimeDesc(advanceHours) {
      if (advanceHours === 0) {
        return '立即取消(当前时间)';
      } else if (advanceHours < 24) {
        return `提前${advanceHours}小时`;
      } else {
        const days = Math.floor(advanceHours / 24);
        const hours = advanceHours % 24;
        if (hours === 0) {
          return `提前${days}天`;
        } else {
          return `提前${days}天${hours}小时`;
        }
      }
    },

    /** 关闭 */
    handleClose() {
      this.$emit('close');
    },
  },
};
</script>

<style scoped>
.result-section {
  margin-top: 20px;
}

.result-card {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 6px;
  margin-bottom: 15px;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
}

.result-item:last-child {
  margin-bottom: 0;
}

.result-item.highlight {
  padding: 8px 0;
  border-top: 1px solid #e4e7ed;
  font-weight: bold;
  font-size: 15px;
}

.label {
  color: #606266;
}

.value {
  color: #303133;
  font-weight: 500;
}

.value.fee {
  color: #f56c6c;
  font-size: 16px;
}

.value.refund {
  color: #67c23a;
  font-size: 16px;
}

.fee-explanation {
  background-color: #fafafa;
  padding: 15px;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.explanation-title {
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
  font-size: 14px;
}

.explanation-item {
  display: flex;
  align-items: center;
  font-size: 13px;
  line-height: 1.5;
}

.explanation-item i {
  margin-right: 6px;
  font-size: 14px;
}

.explanation-item.success {
  color: #67c23a;
}

.explanation-item.warning {
  color: #e6a23c;
}
</style>
