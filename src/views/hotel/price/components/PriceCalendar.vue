<template>
  <el-dialog
    title="价格日历"
    :visible="visible"
    width="900px"
    append-to-body
    @close="handleClose"
  >
    <div class="calendar-header">
      <el-form inline>
        <el-form-item label="房型：">
          <el-select v-model="selectedRoomId" placeholder="请选择房型" @change="handleRoomChange">
            <el-option
              v-for="room in roomList"
              :key="room.id"
              :label="room.roomName"
              :value="room.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="年月：">
          <el-date-picker
            v-model="selectedDate"
            type="month"
            placeholder="请选择年月"
            value-format="yyyy-MM"
            @change="handleDateChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadCalendar" :loading="loading">查询</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="calendar-content" v-loading="loading">
      <div v-if="!selectedRoomId" class="empty-tip">
        请先选择房型
      </div>
      <div v-else-if="!calendarData || !calendarData.dayPrices || calendarData.dayPrices.length === 0"
           class="empty-tip">
        暂无数据
      </div>
      <div v-else-if="calendarData && calendarData.dayPrices" class="calendar-grid">
        <div class="calendar-info">
          <h3>{{ calendarData.roomName }} - {{ calendarData.year }}年{{ calendarData.month }}月</h3>
        </div>

        <!-- 日历头部 -->
        <div class="calendar-header-row">
          <div class="calendar-cell header">天数</div>
          <div class="calendar-cell header">日期</div>
          <div class="calendar-cell header">星期</div>
          <div class="calendar-cell header">日期类型</div>
          <div class="calendar-cell header">价格</div>
          <div class="calendar-cell header">策略名称</div>
        </div>

        <!-- 日历内容 -->
        <div
          v-for="dayPrice in calendarData.dayPrices"
          :key="dayPrice.day"
          class="calendar-row"
          :class="{
            'weekend': dayPrice.dateType === 'WEEKEND',
            'holiday': dayPrice.dateType === 'HOLIDAY',
            'workday': dayPrice.dateType === 'WORKDAY' || dayPrice.dateType === 'TRANSFER_WORKDAY',
            'no-strategy': !dayPrice.hasStrategy
          }"
        >
          <div class="calendar-cell">{{ dayPrice.day }}</div>
          <div class="calendar-cell">{{ dayPrice.date }}</div>
          <div class="calendar-cell">{{ getWeekDay(dayPrice.date) }}</div>
          <div class="calendar-cell">
            <el-tag
              :type="getDateTypeTagType(dayPrice.dateType)"
              size="mini"
            >
              {{ dayPrice.dateTypeDesc }}
            </el-tag>
          </div>
          <div class="calendar-cell price">
            <span v-if="dayPrice.hasStrategy" class="price-value">¥{{ dayPrice.price }}</span>
            <span v-else class="no-price">无价格</span>
          </div>
          <div class="calendar-cell">
            <span v-if="dayPrice.hasStrategy">{{ dayPrice.strategyName }}</span>
            <span v-else class="no-strategy-text">{{ dayPrice.strategyName }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 图例 -->
    <div class="calendar-legend">
      <div class="legend-item">
        <div class="legend-color workday"></div>
        <span>工作日</span>
      </div>
      <div class="legend-item">
        <div class="legend-color weekend"></div>
        <span>周末</span>
      </div>
      <div class="legend-item">
        <div class="legend-color holiday"></div>
        <span>节假日</span>
      </div>
      <div class="legend-item">
        <div class="legend-color no-strategy"></div>
        <span>无策略</span>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {hotelPriceCalendarApi} from '@/api/hotel';

export default {
  name: 'PriceCalendar',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    roomList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      loading: false,
      selectedRoomId: null,
      selectedDate: '',
      calendarData: {
        roomId: null,
        roomName: '',
        year: 0,
        month: 0,
        dayPrices: [],
      },
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.initCalendar();
      } else {
        // 关闭时重置数据，避免下次打开时显示旧数据
        this.resetCalendarData();
      }
    },
  },
  methods: {
    /** 重置日历数据 */
    resetCalendarData() {
      this.calendarData = {
        roomId: null,
        roomName: '',
        year: 0,
        month: 0,
        dayPrices: [],
      };
      this.selectedRoomId = null;
      this.selectedDate = '';
    },

    /** 初始化日历 */
    initCalendar() {
      // 先重置数据
      this.resetCalendarData();

      const now = new Date();
      this.selectedDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
      if (this.roomList.length > 0) {
        this.selectedRoomId = this.roomList[0].id;
        this.loadCalendar();
      }
    },

    /** 房型变化 */
    handleRoomChange() {
      if (this.selectedRoomId && this.selectedDate) {
        this.loadCalendar();
      }
    },

    /** 日期变化 */
    handleDateChange() {
      if (this.selectedRoomId && this.selectedDate) {
        this.loadCalendar();
      }
    },

    /** 加载日历数据 */
    loadCalendar() {
      if (!this.selectedRoomId || !this.selectedDate) {
        return;
      }

      const [year, month] = this.selectedDate.split('-');
      this.loading = true;

      hotelPriceCalendarApi({
        roomId: this.selectedRoomId,
        year: parseInt(year),
        month: parseInt(month),
      })
        .then((response) => {
          // 安全检查：确保返回的数据结构正确
          if (response && response.roomId !== undefined) {
            this.calendarData = {
              roomId: response.roomId || null,
              roomName: response.roomName || '',
              year: response.year || 0,
              month: response.month || 0,
              dayPrices: response.dayPrices || [],
            };
          } else {
            // 如果没有数据，重置为默认值
            this.calendarData = {
              roomId: null,
              roomName: '',
              year: 0,
              month: 0,
              dayPrices: [],
            };
          }
        })
        .catch((error) => {
          console.error('获取价格日历失败:', error);
          this.$modal.msgError('获取价格日历失败');
          // 重置数据
          this.calendarData = {
            roomId: null,
            roomName: '',
            year: 0,
            month: 0,
            dayPrices: [],
          };
        })
        .finally(() => {
          this.loading = false;
        });
    },

    /** 获取星期 */
    getWeekDay(dateStr) {
      const weekDays = ['日', '一', '二', '三', '四', '五', '六'];
      const date = new Date(dateStr);
      return '周' + weekDays[date.getDay()];
    },

    /** 获取日期类型标签类型 */
    getDateTypeTagType(dateType) {
      switch (dateType) {
        case 'WORKDAY':
        case 'TRANSFER_WORKDAY':
          return '';
        case 'WEEKEND':
          return 'warning';
        case 'HOLIDAY':
          return 'danger';
        default:
          return 'info';
      }
    },

    /** 关闭 */
    handleClose() {
      this.$emit('close');
    },
  },
};
</script>

<style scoped>
.calendar-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.calendar-content {
  min-height: 400px;
}

.empty-tip {
  text-align: center;
  color: #999;
  padding: 50px 0;
  font-size: 14px;
}

.calendar-info {
  margin-bottom: 15px;
}

.calendar-info h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
}

.calendar-grid {
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.calendar-header-row {
  display: flex;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.calendar-row {
  display: flex;
  border-bottom: 1px solid #ebeef5;
}

.calendar-row:last-child {
  border-bottom: none;
}

.calendar-cell {
  flex: 1;
  padding: 8px 12px;
  text-align: center;
  border-right: 1px solid #ebeef5;
  font-size: 13px;
}

.calendar-cell:last-child {
  border-right: none;
}

.calendar-cell.header {
  font-weight: bold;
  color: #303133;
}

.calendar-cell.price {
  font-weight: bold;
}

.price-value {
  color: #67c23a;
}

.no-price {
  color: #f56c6c;
}

.no-strategy-text {
  color: #909399;
}

/* 日期类型样式 */
.calendar-row.workday {
  background-color: #f0f9ff;
}

.calendar-row.weekend {
  background-color: #fef0e6;
}

.calendar-row.holiday {
  background-color: #fef0f0;
}

.calendar-row.no-strategy {
  background-color: #f5f5f5;
  color: #999;
}

/* 图例 */
.calendar-legend {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

.legend-item {
  display: flex;
  align-items: center;
  margin: 0 15px;
  font-size: 12px;
}

.legend-color {
  width: 16px;
  height: 16px;
  margin-right: 6px;
  border-radius: 2px;
}

.legend-color.workday {
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
}

.legend-color.weekend {
  background-color: #fef0e6;
  border: 1px solid #f0c78a;
}

.legend-color.holiday {
  background-color: #fef0f0;
  border: 1px solid #f0a0a0;
}

.legend-color.no-strategy {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
}
</style>
