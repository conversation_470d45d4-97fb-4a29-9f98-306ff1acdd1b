<template>
  <el-dialog
    :title="isEdit ? '编辑价格策略' : (formData.id ? '查看价格策略' : '新增价格策略')"
    :visible="visible"
    width="700px"
    append-to-body
    @close="handleClose"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
      :disabled="!isEdit && formData.id"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="房型" prop="roomId">
            <el-select v-model="form.roomId" placeholder="请选择房型" style="width: 100%">
              <el-option
                v-for="room in roomList"
                :key="room.id"
                :label="room.roomName"
                :value="room.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <!--        <el-col :span="12">-->
        <!--          <el-form-item label="策略名称" prop="strategyName">-->
        <!--            <el-input v-model="form.strategyName" placeholder="请输入策略名称" />-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="策略类型" prop="strategyType">
            <el-select v-model="form.strategyType" placeholder="请选择策略类型" style="width: 100%"
                       @change="handleStrategyTypeChange">
              <el-option label="基础价格(工作日)" :value="1"/>
              <el-option label="周末价格" :value="2"/>
              <el-option label="节假日价格" :value="3"/>
              <!--              <el-option label="按日期范围" :value="4" />-->
              <!--              <el-option label="按具体日期" :value="5" />-->
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="价格值" prop="priceValue">
            <el-input-number
              v-model="form.priceValue"
              :min="0.01"
              :max="99999.99"
              :precision="2"
              placeholder="请输入价格值"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <!--          <el-form-item label="优先级" prop="priority">-->
          <!--            <el-input-number-->
          <!--              v-model="form.priority"-->
          <!--              :min="1"-->
          <!--              :max="999"-->
          <!--              placeholder="请输入优先级"-->
          <!--              style="width: 100%"-->
          <!--            />-->
          <!--            <div style="font-size: 12px; color: #999; margin-top: 4px;">数值越大优先级越高</div>-->
          <!--          </el-form-item>-->
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 日期范围策略 -->
      <el-row :gutter="20" v-if="form.strategyType === 4">
        <el-col :span="12">
          <el-form-item label="开始日期" prop="startDate">
            <el-date-picker
              v-model="form.startDate"
              type="date"
              placeholder="请选择开始日期"
              style="width: 100%"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束日期" prop="endDate">
            <el-date-picker
              v-model="form.endDate"
              type="date"
              placeholder="请选择结束日期"
              style="width: 100%"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 具体日期策略 -->
      <el-form-item label="具体日期" prop="specificDates" v-if="form.strategyType === 5">
        <el-date-picker
          v-model="specificDatesArray"
          type="dates"
          placeholder="请选择具体日期"
          style="width: 100%"
          value-format="yyyy-MM-dd"
          @change="handleSpecificDatesChange"
        />
        <div style="font-size: 12px; color: #999; margin-top: 4px;">可选择多个具体日期</div>
      </el-form-item>

      <!-- 适用星期 -->
      <!--      <el-form-item label="适用星期" prop="weekDays" v-if="form.strategyType <= 3">-->
      <!--        <el-checkbox-group v-model="weekDaysChecked" @change="handleWeekDaysChange">-->
      <!--          <el-checkbox label="1">周一</el-checkbox>-->
      <!--          <el-checkbox label="2">周二</el-checkbox>-->
      <!--          <el-checkbox label="3">周三</el-checkbox>-->
      <!--          <el-checkbox label="4">周四</el-checkbox>-->
      <!--          <el-checkbox label="5">周五</el-checkbox>-->
      <!--          <el-checkbox label="6">周六</el-checkbox>-->
      <!--          <el-checkbox label="7">周日</el-checkbox>-->
      <!--        </el-checkbox-group>-->
      <!--      </el-form-item>-->

      <el-form-item label="策略描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入策略描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer" v-if="isEdit || !formData.id">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {hotelPriceCreateApi, hotelPriceUpdateApi} from '@/api/hotel';

export default {
  name: 'PriceForm',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: () => ({}),
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    roomList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      form: {
        id: null,
        roomId: null,
        strategyName: '',
        strategyType: null,
        weekDays: '',
        startDate: '',
        endDate: '',
        specificDates: '',
        priceValue: null,
        priority: 1,
        status: 1,
        description: '',
      },
      weekDaysChecked: [],
      specificDatesArray: [],
      submitLoading: false,
      rules: {
        roomId: [
          {required: true, message: '房型不能为空', trigger: 'change'},
        ],
        strategyName: [
          {required: true, message: '策略名称不能为空', trigger: 'blur'},
          {max: 100, message: '策略名称长度不能超过100个字符', trigger: 'blur'},
        ],
        strategyType: [
          {required: true, message: '策略类型不能为空', trigger: 'change'},
        ],
        priceValue: [
          {required: true, message: '价格值不能为空', trigger: 'blur'},
        ],
        priority: [
          {required: true, message: '优先级不能为空', trigger: 'blur'},
        ],
        status: [
          {required: true, message: '状态不能为空', trigger: 'change'},
        ],
        startDate: [
          {required: true, message: '开始日期不能为空', trigger: 'change'},
        ],
        endDate: [
          {required: true, message: '结束日期不能为空', trigger: 'change'},
        ],
        specificDates: [
          {required: true, message: '具体日期不能为空', trigger: 'change'},
        ],
      },
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm();
      }
    },
    formData: {
      handler(val) {
        if (val && this.visible) {
          this.initForm();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    /** 初始化表单 */
    initForm() {
      if (this.formData.id) {
        this.form = {...this.formData};
        // 解析适用星期
        if (this.form.weekDays) {
          this.weekDaysChecked = this.form.weekDays.split(',');
        }
        // 解析具体日期
        if (this.form.specificDates) {
          try {
            this.specificDatesArray = JSON.parse(this.form.specificDates);
          } catch (e) {
            this.specificDatesArray = [];
          }
        }
      } else {
        this.form = {
          id: null,
          roomId: null,
          strategyName: '',
          strategyType: null,
          weekDays: '',
          startDate: '',
          endDate: '',
          specificDates: '',
          priceValue: null,
          priority: 1,
          status: 1,
          description: '',
        };
        this.weekDaysChecked = [];
        this.specificDatesArray = [];
      }
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
        }
      });
    },

    /** 策略类型变化 */
    handleStrategyTypeChange(value) {
      // 清空相关字段
      this.form.weekDays = '';
      this.form.startDate = '';
      this.form.endDate = '';
      this.form.specificDates = '';
      this.weekDaysChecked = [];
      this.specificDatesArray = [];

      // 设置默认优先级
      const priorityMap = {
        1: 10,  // 基础价格
        2: 50,  // 周末价格
        3: 100, // 节假日价格
        4: 80,  // 日期范围
        5: 90,  // 具体日期
      };
      this.form.priority = priorityMap[value] || 1;
    },

    /** 适用星期变化 */
    handleWeekDaysChange(value) {
      this.form.weekDays = value.join(',');
    },

    /** 具体日期变化 */
    handleSpecificDatesChange(value) {
      this.form.specificDates = JSON.stringify(value || []);
    },

    /** 提交 */
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 验证日期范围
          if (this.form.strategyType === 4) {
            if (!this.form.startDate || !this.form.endDate) {
              this.$modal.msgError('日期范围策略必须设置开始日期和结束日期');
              return;
            }
            if (new Date(this.form.startDate) > new Date(this.form.endDate)) {
              this.$modal.msgError('开始日期不能晚于结束日期');
              return;
            }
          }

          // 验证具体日期
          if (this.form.strategyType === 5) {
            if (!this.specificDatesArray || this.specificDatesArray.length === 0) {
              this.$modal.msgError('具体日期策略必须设置具体日期');
              return;
            }
          }

          this.submitLoading = true;
          const api = this.form.id ? hotelPriceUpdateApi : hotelPriceCreateApi;
          api(this.form)
            .then(() => {
              this.$modal.msgSuccess(this.form.id ? '修改成功' : '新增成功');
              this.$emit('success');
            })
            .finally(() => {
              this.submitLoading = false;
            });
        }
      });
    },

    /** 关闭 */
    handleClose() {
      this.$emit('close');
    },
  },
};
</script>
