<template>
  <div class="divBox relative">
    <!-- 搜索条件 -->
    <el-card
      v-if="$auth.hasPermi('admin:hotel:price:list')"
      :bordered="false"
      shadow="never"
      class="ivu-mt"
      :body-style="{ padding: 0 }"
    >
      <div class="padding-add">
        <el-form inline size="small" label-position="right" @submit.native.prevent>
          <el-form-item label="房型：">
            <el-select
              v-model="searchForm.roomId"
              clearable
              size="small"
              placeholder="请选择房型"
              class="form_content_width"
              @change="handleSearch"
            >
              <el-option
                v-for="room in roomList"
                :key="room.id"
                :label="room.roomName"
                :value="room.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="策略名称：">
            <el-input
              v-model.trim="searchForm.strategyName"
              placeholder="请输入策略名称"
              class="form_content_width"
              size="small"
              @keyup.enter.native="handleSearch"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="策略类型：">
            <el-select
              v-model="searchForm.strategyType"
              clearable
              size="small"
              placeholder="请选择策略类型"
              class="form_content_width"
            >
              <el-option label="基础价格(工作日)" :value="1"/>
              <el-option label="周末价格" :value="2"/>
              <el-option label="节假日价格" :value="3"/>
              <el-option label="按日期范围" :value="4"/>
              <el-option label="按具体日期" :value="5"/>
            </el-select>
          </el-form-item>
          <el-form-item label="状态：">
            <el-select
              v-model="searchForm.status"
              clearable
              size="small"
              placeholder="请选择状态"
              class="selWidth"
            >
              <el-option label="启用" :value="1"/>
              <el-option label="禁用" :value="0"/>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" @click="handleSearch">查询</el-button>
            <el-button size="small" @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 操作按钮和表格 -->
    <el-card class="box-card mt14" :body-style="{ padding: '0 20px 20px' }" shadow="never" :bordered="false">
      <div class="clearfix mb20">
        <el-button size="small" type="primary" v-hasPermi="['admin:hotel:price:save']" @click="handleAdd">
          添加价格策略
        </el-button>
        <el-button size="small" type="success" @click="handleCalendar" v-hasPermi="['admin:hotel:price:calendar']">
          价格日历
        </el-button>
      </div>

      <!-- 表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column prop="roomName" label="房型名称" min-width="120"/>
        <el-table-column prop="strategyName" label="策略名称" min-width="120"/>
        <el-table-column prop="strategyTypeDesc" label="策略类型" min-width="120"/>
        <el-table-column prop="priceValue" label="价格值" width="100" align="center">
          <template slot-scope="scope">
            ¥{{ scope.row.priceValue }}
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" width="80" align="center"/>
        <!--        <el-table-column prop="startDate" label="开始日期" width="120" align="center">-->
        <!--          <template slot-scope="scope">-->
        <!--            {{ scope.row.startDate ? parseTime(scope.row.startDate, '{y}-{m}-{d}') : '-' }}-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <!--        <el-table-column prop="endDate" label="结束日期" width="120" align="center">-->
        <!--          <template slot-scope="scope">-->
        <!--            {{ scope.row.endDate ? parseTime(scope.row.endDate, '{y}-{m}-{d}') : '-' }}-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(scope.row)"
              v-hasPermi="['admin:hotel:price:status']"
            />
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160" align="center">
          <template slot-scope="scope">
            {{ parseTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="190" align="center" fixed="right">
          <template slot-scope="scope">
            <a @click="handleView(scope.row)" v-hasPermi="['admin:hotel:price:info']">查看</a>
            <el-divider direction="vertical"></el-divider>
            <a @click="handleEdit(scope.row)" v-hasPermi="['admin:hotel:price:update']">编辑</a>
            <el-divider direction="vertical"></el-divider>
            <a @click="handleDelete(scope.row)" style="color: #f56c6c"
               v-hasPermi="['admin:hotel:price:delete']">删除</a>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="block">
        <el-pagination
          v-show="total > 0"
          background
          :page-sizes="[10, 20, 30, 40]"
          :page-size="queryParams.limit"
          :current-page="queryParams.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <price-form
      v-if="formVisible"
      :visible="formVisible"
      :form-data="formData"
      :is-edit="isEdit"
      :room-list="roomList"
      @close="handleFormClose"
      @success="handleFormSuccess"
    />

    <!-- 价格日历对话框 -->
    <price-calendar
      v-if="calendarVisible"
      :visible="calendarVisible"
      :room-list="roomList"
      @close="handleCalendarClose"
    />
  </div>
</template>

<script>
import {hotelPriceDeleteApi, hotelPriceListApi, hotelPriceStatusApi, hotelRoomListApi} from '@/api/hotel';
import PriceForm from './components/PriceForm.vue';
import PriceCalendar from './components/PriceCalendar.vue';
import {parseTime} from '@/utils/index';
// import Pagination from '@/components/Pagination'; // 移除不存在的组件导入

export default {
  name: 'HotelPrice',
  components: {
    PriceForm,
    PriceCalendar,
    // Pagination, // 移除不存在的组件
  },
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0,
      queryParams: {
        page: 1,
        limit: 10,
      },
      searchForm: {
        roomId: null,
        strategyName: '',
        strategyType: null,
        status: null,
      },
      selectedRows: [],
      formVisible: false,
      formData: {},
      isEdit: false,
      calendarVisible: false,
      roomList: [],
    };
  },
  created() {
    this.getRoomList();
    this.getList();
  },
  methods: {
    parseTime,

    /** 获取房型列表 */
    getRoomList() {
      hotelRoomListApi({page: 1, limit: 1000, status: 1}).then((response) => {
        // 修复数据解析逻辑 - axios拦截器已经返回了res.data
        this.roomList = response.list || [];
      });
    },

    /** 查询列表 */
    getList() {
      this.loading = true;
      const params = {
        ...this.queryParams,
        ...this.searchForm,
      };
      hotelPriceListApi(params)
        .then((response) => {
          // 修复数据解析逻辑 - axios拦截器已经返回了res.data
          console.log('价格策略API响应数据:', response);
          if (response && response.list) {
            this.tableData = response.list;
            this.total = response.total || 0;
          } else if (response && Array.isArray(response)) {
            this.tableData = response;
            this.total = response.length;
          } else {
            console.warn('价格策略列表数据结构异常:', response);
            this.tableData = [];
            this.total = 0;
          }
        })
        .catch((error) => {
          console.error('获取价格策略列表失败:', error);
          this.tableData = [];
          this.total = 0;
          this.$message.error('获取价格策略列表失败，请稍后重试');
        })
        .finally(() => {
          this.loading = false;
        });
    },

    /** 搜索 */
    handleSearch() {
      this.queryParams.page = 1;
      this.getList();
    },

    /** 重置 */
    handleReset() {
      this.searchForm = {
        roomId: null,
        strategyName: '',
        strategyType: null,
        status: null,
      };
      this.handleSearch();
    },

    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },

    /** 新增 */
    handleAdd() {
      this.formData = {};
      this.isEdit = false;
      this.formVisible = true;
    },

    /** 查看 */
    handleView(row) {
      this.formData = {...row};
      this.isEdit = false;
      this.formVisible = true;
    },

    /** 编辑 */
    handleEdit(row) {
      this.formData = {...row};
      this.isEdit = true;
      this.formVisible = true;
    },

    /** 删除 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除价格策略"' + row.strategyName + '"？').then(() => {
        return hotelPriceDeleteApi(row.id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess('删除成功');
      });
    },

    /** 状态修改 */
    handleStatusChange(row) {
      const text = row.status === 1 ? '启用' : '禁用';
      this.$modal.confirm('确认要"' + text + '""' + row.strategyName + '"价格策略吗？').then(() => {
        return hotelPriceStatusApi(row.id, row.status);
      }).then(() => {
        this.$modal.msgSuccess(text + '成功');
      }).catch(() => {
        row.status = row.status === 0 ? 1 : 0;
      });
    },

    /** 价格日历 */
    handleCalendar() {
      this.calendarVisible = true;
    },

    /** 表单关闭 */
    handleFormClose() {
      this.formVisible = false;
    },

    /** 表单提交成功 */
    handleFormSuccess() {
      this.formVisible = false;
      this.getList();
    },

    /** 日历关闭 */
    handleCalendarClose() {
      this.calendarVisible = false;
    },

    /** 分页大小改变 */
    handleSizeChange(val) {
      this.queryParams.limit = val;
      this.queryParams.page = 1;
      this.getList();
    },

    /** 当前页改变 */
    handleCurrentChange(val) {
      this.queryParams.page = val;
      this.getList();
    },
  },
};
</script>

<style scoped>
.form_content_width {
  width: 200px;
}

.selWidth {
  width: 120px;
}
</style>
