// 导出TableHead 数据
export const GoodsTableHead = [
  {
    title: '售价',
    slot: 'price',
    align: 'center',
    minWidth: '100px',
  },
  {
    title: '成本价',
    slot: 'cost',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '划线价',
    slot: 'otPrice',
    align: 'center',
    minWidth: '100px',
  },
  {
    title: '库存',
    slot: 'stock',
    align: 'center',
    minWidth: '80px',
  },
  {
    title: '商品编码',
    slot: 'barCode',
    align: 'center',
    minWidth: '120px',
  },
  {
    title: '商品条码',
    slot: 'itemNumber',
    align: 'center',
    minWidth: '120px',
  },
  {
    title: '重量（KG）',
    slot: 'weight',
    align: 'center',
    minWidth: '90px',
  },
  {
    title: '体积(m³)',
    slot: 'volume',
    align: 'center',
    minWidth: '80px',
  },
  {
    title: '默认选中规格',
    slot: 'isDefault',
    fixed: 'right',
    align: 'center',
    minWidth: '100px',
  },
  {
    title: '操作',
    slot: 'action',
    fixed: 'right',
    align: 'center',
    minWidth: '120px',
  },
];

// 佣金
export const commissionTableHead = [
  {
    title: '一级返佣(%)',
    slot: 'brokerage',
    align: 'center',
    minWidth: 100,
  },
  {
    title: '二级返佣(%)',
    slot: 'brokerageTwo',
    align: 'center',
    minWidth: 100,
  },
];

// 图片
export const imageTableHead = [
  {
    title: '图片',
    slot: 'image',
    align: 'center',
    minWidth: 60,
  },
];

// 会员价
export const vipPriceTableHead = [
  {
    title: '会员价',
    slot: 'vipPrice',
    align: 'center',
    minWidth: 120,
  },
];

// 云盘
export const VirtualTableHead = [
  {
    title: '售价',
    slot: 'price',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '成本价',
    slot: 'cost',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '划线价',
    slot: 'otPrice',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '库存',
    slot: 'stock',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '商品编码',
    slot: 'barCode',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '云盘设置',
    slot: 'fictitious',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '默认选中规格',
    slot: 'isDefault',
    fixed: 'right',
    align: 'center',
    minWidth: 90,
  },
  {
    title: '操作',
    slot: 'action',
    fixed: 'right',
    align: 'center',
    minWidth: 120,
  },
];
//   卡密设置
export const VirtualTableHead2 = [
  {
    title: '售价',
    slot: 'price',
    align: 'center',
    minWidth: 100,
  },
  {
    title: '成本价',
    slot: 'cost',
    align: 'center',
    minWidth: 100,
  },
  {
    title: '划线价',
    slot: 'otPrice',
    align: 'center',
    minWidth: 100,
  },
  {
    title: '库存',
    slot: 'stock',
    align: 'center',
    minWidth: 100,
  },
  {
    title: '商品编码',
    slot: 'barCode',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '卡密设置',
    slot: 'fictitious',
    align: 'center',
    minWidth: 150,
  },
  {
    title: '默认选中规格',
    slot: 'isDefault',
    fixed: 'right',
    align: 'center',
    minWidth: 90,
  },
  {
    title: '操作',
    slot: 'action',
    fixed: 'right',
    align: 'center',
    minWidth: 120,
  },
];

//虚拟商品
export const FictitiousTableHead = [
  {
    title: '售价',
    slot: 'price',
    align: 'center',
    minWidth: '100px',
  },
  {
    title: '成本价',
    slot: 'cost',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '划线价',
    slot: 'otPrice',
    align: 'center',
    minWidth: '100px',
  },
  {
    title: '库存',
    slot: 'stock',
    align: 'center',
    minWidth: '80px',
  },
  {
    title: '商品编码',
    slot: 'barCode',
    align: 'center',
    minWidth: '120px',
  },
  {
    title: '重量（KG）',
    slot: 'weight',
    align: 'center',
    width: '90px',
  },
  {
    title: '体积(m³)',
    slot: 'volume',
    align: 'center',
    minWidth: '80px',
  },
  {
    title: '默认选中规格',
    slot: 'isDefault',
    fixed: 'right',
    align: 'center',
    minWidth: '100px',
  },
  {
    title: '操作',
    slot: 'action',
    fixed: 'right',
    align: 'center',
    minWidth: '120px',
  },
];
